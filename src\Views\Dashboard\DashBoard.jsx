import React, { useState, useRef, memo, useEffect, useMemo } from "react";
import LeftNav from "../../Components/LeftNav";
import "../../Components/leftnav.css";
import TitleBar from "../../Components/TitleBar";
import "../../Components/titlenav.css";
import "./dashboard.css";
import { BiSort } from "react-icons/bi";
import { MdFilterAlt } from "react-icons/md";
import { useLocation, useNavigate } from "react-router-dom";
import Cookies from "universal-cookie";
import filter_icon from '../../assets/filter_icon.svg'
import clear_search from '../../assets/clear_search.svg'
import { MdOutlineYoutubeSearchedFor } from "react-icons/md";
import { FaPhone } from 'react-icons/fa';
import { FaWhatsapp } from 'react-icons/fa';
import { BsMicrosoftTeams } from "react-icons/bs";
import { RiCalendarScheduleFill } from "react-icons/ri";
import { IoMdSearch } from "react-icons/io";
import { FaSort, FaSortUp, FaSortDown } from "react-icons/fa";
import { BsSortUp } from "react-icons/bs";
import { BsSortDown } from "react-icons/bs";
import { setMeetings, setError } from "../../store/slices/meetingslice";
// import { fetchMeetings } from "../Views/utilities";
// import Calendar from 'react-calendar';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import 'react-big-calendar/lib/css/react-big-calendar.css';

import { VscClearAll } from "react-icons/vsc";
import { Radio, ThreeDots } from "react-loader-spinner";
import { RiEyeLine, RiEyeOffLine } from "react-icons/ri";
import * as XLSX from "xlsx";
import { PiMicrosoftExcelLogoFill } from "react-icons/pi";
import { MdMessage } from "react-icons/md";
import CryptoJS from "crypto-js";
import Error from "../../assets/error.jpg"
import {
  setDatesSelected, setNamesSelected,
  setjobIdsSelected,
  setEmailsSelected,
  setMobilesSelected,
  setclientsSelected,
  setprofilesSelected,
  setskillsSelected,
  setrecruitersSelected, setstatussSelected
} from '../../store/slices/filterSlice.js'

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faFileAlt,
  faInfoCircle,
  faSyncAlt,
  faPen,
  faTrashAlt,
  faCalendarAlt,
  faDownload,
  faShareAlt
} from "@fortawesome/free-solid-svg-icons";
import { FaAngleLeft } from "react-icons/fa6";
import { FaAngleRight } from "react-icons/fa6";
import { toast } from "react-toastify";
import { Bounce } from "react-toastify";
import { setDashboardData } from "../../store/slices/dashboardSlice";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import Modal from "react-modal";
import { Hourglass } from "react-loader-spinner";
import { Tooltip as ReactTooltip } from "react-tooltip";
import { motion } from "framer-motion";
import ScheduleMeet from "../../Components/schedulemeet";
import { fetchMeetings } from "../utilities";
import { getDashboardData } from "../utilities.js";
import { store } from "../../store/store";
const cookies = new Cookies();

function DashBoard() {
  const leftNavRef = useRef();

  const { namesSelected: namesSelectedRdx,
    datesSelected: datesSelectedRdx,
    jobIdsSelected: jobIdsSelectedRdx,
    emailsSelected: emailsSelectedRdx,
    mobilesSelected: mobilesSelectedRdx,
    clientsSelected: clientsSelectedRdx,
    profilesSelected: profilesSelectedRdx,
    skillsSelected: skillsSelectedRdx,
    recruitersSelected: recruitersSelectedRdx,
    statussSelected: statussSelectedRdx

  } = useSelector((state) => state.filterSliceReducer);
  const openModals = () => {
    // console.log('Opening calendar from DashBoard');
    if (leftNavRef.current) {
      leftNavRef.current.open();
    }
    setIsModalOpen(false);
    setInterviewModal(false);
  };

  const closeModals = () => {
    // console.log('Closing calendar from DashBoard');
    if (leftNavRef.current) {
      leftNavRef.current.close();
    }
  };
  const USERTYPE = cookies.get("USERTYPE");

  const [dashboard, setDashboard] = useState([]);
  const navigate = useNavigate();
  const [showRadio, setshowRadio] = useState(false);
  const [selectedScheduleData, setSelectedScheduleData] = useState(null);
  const [interviewModal, setInterviewModal] = useState(false);
  const openModal = (item) => {
    setSelectedScheduleData({
      job_id: item.job_id,
      name: item.name,
      email: item.email,
      client: item.client,
      profile: item.profile,
      status: item.status,
      // Add any other data you need to pass
    });
    setInterviewModal(true);
  };
  // console.log(selectedScheduleData,"passingdata")
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(\.[a-zA-Z]{2,})*$/;

  // Function to validate email before adding
  const validateEmail = (email) => {
    return emailRegex.test(email);
  };

  const handleAddEmailWithValidation = () => {
    if (validateEmail(searchQuery)) {
      handleAddEmail();
    } else {
      toast.warn('Please enter a valid email address');
    }
  };
  const handleAddEmailWithValidations = () => {
    if (validateEmail(searchQuery2)) {
      handleEmailChange(searchQuery2, 'dropdown2');
      setSearchQuery2('');
    } else {
      toast.warn('Please enter a valid email address');
    }
  };

  const handlecloseModal = () => {
    setInterviewModal(false);
    setTitle("");
    setStartDate("");
    setEndDate("");
    setStartTime("");
    setEndTime("");
    setSelectedTimeZone("");
    setSelectedEmails([]);
    setSelectedEmails2([]);
    setSearchQuery('');
    setSearchQuery2('');

  }

  // const InterviewcloseModal = () => {
  //   setInterviewModal(false);
  // };
  const localizer = momentLocalizer(moment);
  const InterviewcloseModal = () => {
    setInterviewModal(false);
    // setInterviewModal(false);
    setTitle("");
    setStartDate("");
    setEndDate("");
    setStartTime("");
    setEndTime("");
    setSelectedTimeZone("");
    setSelectedEmails([]);
    setSelectedEmails2([]);
    setSearchQuery('');
    setSearchQuery2('');

  };
  const [startDate, setStartDate] = useState(() => {
    const today = new Date().toISOString().split('T')[0];
    return today;
  });
  const [endDate, setEndDate] = useState("");
  const [error, seterror] = useState('');
  useEffect(() => {
    if (startDate) {
      setEndDate(startDate);
    }
  }, [startDate]);

  const handleStartDateChange = (e) => {
    const selectedStartDate = new Date(e.target.value);
    const today = new Date();

    // Set the time to midnight for accurate comparison
    today.setHours(0, 0, 0, 0);
    selectedStartDate.setHours(0, 0, 0, 0);

    // Get current year and month
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth();

    // Get the selected year and month
    const selectedYear = selectedStartDate.getFullYear();
    const selectedMonth = selectedStartDate.getMonth();

    // Validation
    if (
      selectedStartDate < today || // Cannot be in the past
      (selectedYear < currentYear || // Cannot be from a previous year
        (selectedYear === currentYear && selectedMonth < currentMonth)) // Cannot be from a previous month in the current year
    ) {
      setError('Start Date must be today or a future date in this month, next month, or next year.');
      toast.error('Start Date must be today or a future date in this month, next month, or next year.');
    } else {
      setError('');
      setStartDate(e.target.value);
    }
  };

  const handleEndDateChange = (e) => {
    const newEndDate = e.target.value;
    setEndDate(newEndDate);

    if (new Date(startDate) > new Date(newEndDate)) {
      // seterror('End Date is Earlier than Start Date .');
      toast.error('End Date is Earlier than Start Date.');
    } else {
      seterror(''); // Clear error if validation passes
    }

  };



  const [selectedEmails, setSelectedEmails] = useState([]);
  const [isDropdownOpen1, setIsDropdownOpen1] = useState(false);
  const toggleDropdown = () => setIsDropdownOpen(!isDropdownOpen);


  const inputRef = useRef(null);

  const [selectAllDate, setSelectAllDate] = useState(false);
  const [uniqueDataDate, setuniqueDataDate] = useState([]);
  const [dateSelected, setdateSelected] = useState(datesSelectedRdx);

  const [selectAll, setSelectAll] = useState(false);
  const [uniqueDataNames, setuniqueDataNames] = useState([]);
  const [nameSelected, setNameSelected] = useState(namesSelectedRdx);

  const [selectAllForJobId, setselectAllForJobId] = useState(false);
  const [uniqueDatajobId, setuniqueDatajobId] = useState([]);
  const [jobIdSelected, setjobIdSelected] = useState(jobIdsSelectedRdx);

  const [selectAllEmail, setSelectAllEmail] = useState(false);
  const [uniqueDataEmail, setuniqueDataEmail] = useState([]);
  const [emailSelected, setEmailSelected] = useState(emailsSelectedRdx);

  const [selectAllMobile, setSelectAllMobile] = useState(false);
  const [uniqueDataMobile, setuniqueDataMobile] = useState([]);
  const [mobileSelected, setMobileSelected] = useState(mobilesSelectedRdx);
  const [selectedShares, setSelectedShares] = useState([]);
  const [selectAllClient, setSelectAllClient] = useState(false);
  const [uniqueDataClient, setuniqueDataClient] = useState([]);
  const [clientSelected, setclientSelected] = useState(clientsSelectedRdx);

  const [selectAllProfile, setSelectAllProfile] = useState(false);
  const [uniqueDataProfile, setuniqueDataProfile] = useState([]);
  const [profileSelected, setprofileSelected] = useState(profilesSelectedRdx);

  const [selectAllSkill, setSelectAllSkill] = useState(false);
  const [uniqueDataSkill, setuniqueDataSkill] = useState([]);
  const [skillSelected, setskillSelected] = useState(skillsSelectedRdx);

  const [selectAllRecruiter, setSelectAllRecruiter] = useState(false);
  const [uniqueDataRecruiter, setuniqueDataRecruiter] = useState([]);
  const [recruiterSelected, setrecruiterSelected] = useState(recruitersSelectedRdx);

  const [selectAllStatus, setSelectAllStatus] = useState(false);
  const [uniqueDataStatus, setuniqueDataStatus] = useState([]);
  const [statusSelected, setstatusSelected] = useState(statussSelectedRdx);
  const [showDetails, setShowDetails] = useState(false);
  // const detailsRef = useRef(null)
  // useEffect(()=>{
  //   setshowRadio(false)
  // },[])
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMessage, setModalMessage] = useState('');
  const loggedInEmail = localStorage.getItem('email').toLowerCase();
  const handleTimeZoneChange = (e) => {
    const newTimeZone = e.target.value;
    setSelectedTimeZone(newTimeZone);
    // console.log("Selected Time Zone:", newTimeZone); // Log the new value
  };

  useEffect(() => {
    store.dispatch(setDatesSelected({ data: dateSelected }))
    store.dispatch(setNamesSelected({ data: nameSelected }))
    store.dispatch(setjobIdsSelected({ data: jobIdSelected }))
    store.dispatch(setEmailsSelected({ data: emailSelected }))
    store.dispatch(setMobilesSelected({ data: mobileSelected }))
    store.dispatch(setclientsSelected({ data: clientSelected }))
    store.dispatch(setprofilesSelected({ data: profileSelected }))
    store.dispatch(setskillsSelected({ data: skillSelected }))
    store.dispatch(setrecruitersSelected({ data: recruiterSelected }))
    store.dispatch(setstatussSelected({ data: statusSelected }))

  }, [nameSelected, dateSelected, jobIdSelected, emailSelected, mobileSelected, clientSelected, profileSelected, skillSelected, recruiterSelected, statusSelected])
  //  console.log("the issue of the null values",uniqueDataRecruiter)
  // useEffect(() => {
  //   const handleModalClick = (e) => {
  //     const element = document.getElementById("modalId")

  //     // console.log(!detailsRef.current.contains(e.target))
  //     // console.log(document.getElementById("detailsId"))
  //     // console.log(e.target)
  //     if (element !== null) {
  //       if (!document.getElementById("detailsId").contains(e.target) && !element.contains(e.target))
  //         setShowDetails(false)
  //       else
  //         console.log("else")
  //     }
  //   };

  //   window.addEventListener("click", handleModalClick);
  //   return () => {
  //     window.removeEventListener("click", handleModalClick);
  //   }
  // }, [showDetails])


  // const modalRef = useRef(null);
  // const detailsRef = useRef(null);
  // useEffect(() => {
  //   const handleModalClick = (e) => {
  //     if (
  //       modalRef.current && !modalRef.current.contains(e.target) &&
  //       (!detailsRef.current || !detailsRef.current.contains(e.target))
  //     ) {
  //       console.log("Clicked outside the modal. Closing the modal.");
  //       setShowDetails(false);
  //     }
  //   };

  //   if (showDetails) {
  //     document.addEventListener("mousedown", handleModalClick);
  //   }

  //   return () => {
  //     document.removeEventListener("mousedown", handleModalClick);
  //   };
  // }, [showDetails]);


  const modalRef = useRef();

  useEffect(() => {
    // Function to handle clicks outside the modal
    function handleClickOutside(event) {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        handleCloseDetails();
      }
    }

    // Add event listener
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      // Cleanup event listener
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  useEffect(() => {
    if (showDetails) {
      console.log("Details modal is now OPEN");
    } else {
      console.log("Details modal is now CLOSED");
    }
  }, [showDetails]);
  useEffect(() => {
    // console.log(
    //   "uniw list cjanged",
    //   uniqueDataDate,
    //   uniqueDataNames,
    //   uniqueDatajobId,
    //   uniqueDataEmail,
    //   uniqueDataMobile,
    //   uniqueDataClient,
    //   uniqueDataProfile,
    //   uniqueDataSkill,
    //   uniqueDataRecruiter,
    //   uniqueDataStatus,
    // );
  }, [
    uniqueDataDate,
    uniqueDataNames,
    uniqueDatajobId,
    uniqueDataEmail,
    uniqueDataMobile,
    uniqueDataClient,
    uniqueDataProfile,
    uniqueDataSkill,
    uniqueDataRecruiter,
    uniqueDataStatus,
  ]);

  var deleteRow = false;
  const uniRef = useRef(null);
  const [belowCount, setBelowCount] = useState(0);
  const dispatch = useDispatch();
  const [allData, setAllData] = useState({});
  const [isChange, setIsChange] = useState(false);

  const [details, setDetails] = useState({});
  // const [mostUpdatedFilter,setMostUpdatedFilter] = useState([])
  // const {candidateData,listOfCandidates} = useSelector((state)=>state.candidateSliceReducer)
  const { dashboardData } = useSelector((state) => state.dashboardSliceReducer);
  const { recruiters } = useSelector((state) => state.userSliceReducer);
  if (Array.isArray(recruiters)) {
    const recruiteremails = recruiters.map(recruiters => recruiters.email);
  } else {
    console.log("recruiters is not an array or is empty");
  }
  // console.log(recruiters, "recruiters");

  const { managers } = useSelector((state) => state.userSliceReducer);
  if (Array.isArray(managers)) {
    const emails = managers.map(manager => manager.email);
    // console.log(emails, "emails");
  } else {
    console.log("Managers is not an array or is empty");
  }
  // console.log(managers, "managers");
  const [waitForSubmission, setwaitForSubmission] = useState(false);
  const [waitForSubmission1, setwaitForSubmission1] = useState(false);
  const [isDateFiltered, setIsDateFiltered] = useState(false);
  const [isnameFiltered, setIsNameFiltered] = useState(false);
  const [isJobIdFiltered, setIsJobIdFiltered] = useState(false);
  const [isuseridFiltered, setIsUseridFiltered] = useState(false);
  const [ismobileFiltered, setIsMobileFiltered] = useState(false);
  const [isemailFiltered, setIsEmailFiltered] = useState(false);
  const [isclientFiltered, setIsClientFiltered] = useState(false);
  const [isprofileFiltered, setIsProfileFiltered] = useState(false);
  const [isskillFiltered, setIsSkillFiltered] = useState(false);
  const [isrecruiterFiltered, setIsRecruiterFiltered] = useState(false);
  const [isstatusFiltered, setIsStatusFiltered] = useState(false);


  const [startTime, setStartTime] = useState("");
  const [endTime, setEndTime] = useState("");
  const [selectedTimeZone, setSelectedTimeZone] = useState('');
  const [title, setTitle] = useState('');
  const [selectedEmails1, setSelectedEmails1] = useState([]);
  const [selectedEmails2, setSelectedEmails2] = useState([]);
  const [isDropdownOpen, setIsDropdownOpen] = useState(null);



  const dropdownRef1 = useRef(null);
  const dropdownRef2 = useRef(null);
  const inputRef1 = useRef(null);
  const inputRef2 = useRef(null);
  const dropdownRef = useRef(null);
  const handleClickOutside = (event) => {
    if (
      (dropdownRef.current && !dropdownRef.current.contains(event.target)) &&
      (dropdownRef1.current && !dropdownRef1.current.contains(event.target)) &&
      !inputRef.current.contains(event.target)
    ) {
      setIsDropdownOpen(null); // Close all dropdowns
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // const handleDropdownClick = (dropdown) => {
  //   setIsDropdownOpen(isDropdownOpen === dropdown ? null : dropdown);
  // };

  // const handleDropdownClick = (dropdown) => {
  //   setIsDropdownOpen(isDropdownOpen === dropdown ? null : dropdown);
  // };

  const handleDropdownClick = (dropdown) => {
    setIsDropdownOpen(isDropdownOpen === dropdown ? null : dropdown);
  };

  const handleEmailChange = (email, dropdown) => {
    if (dropdown === 'dropdown1') {
      setSelectedEmails1((prevSelectedEmails1) => {
        const emailsArray = Array.isArray(prevSelectedEmails1) ? prevSelectedEmails1 : [];
        return emailsArray.includes(email)
          ? emailsArray.filter((e) => e !== email) // Remove email if already selected
          : [...emailsArray, email]; // Add email if not selected
      });
    } else if (dropdown === 'dropdown2') {
      setSelectedEmails2((prevSelectedEmails2) => {
        const emailsArray = Array.isArray(prevSelectedEmails2) ? prevSelectedEmails2 : [];
        return emailsArray.includes(email)
          ? emailsArray.filter((e) => e !== email) // Remove email if already selected
          : [...emailsArray, email]; // Add email if not selected
      });
      setSearchQuery2('');
      setIsDropdownOpen(false);
    }
  };



  const [searchQuery, setSearchQuery] = useState('');
  const [checkedEmails, setCheckedEmails] = useState({}); // State to track checked emails
  const [newfilteredEmails, setnewFilteredEmails] = useState([]);
  const [showAddButton, setShowAddButton] = useState(false);
  const [searchQuery2, setSearchQuery2] = useState('');
  const [dropdownVisible, setDropdownVisible] = useState(false);

  const handleSearch = (e) => {
    const query = e.target.value;
    setSearchQuery(query);
    const filtered = uniqueDataEmail.filter((email) =>
      email.toLowerCase().includes(query.toLowerCase())
    );
    setnewFilteredEmails(filtered);
    setShowAddButton(query && filtered.length === 0);
    setDropdownVisible(query !== '');
  };

  const handleAddEmail = () => {
    const trimmedQuery = searchQuery.trim();
    if (trimmedQuery && !selectedEmails.includes(trimmedQuery)) {
      setSelectedEmails([...selectedEmails, trimmedQuery]);
    }
    setSearchQuery('');
    setShowAddButton(false);
    setDropdownVisible(false);
  };

  // const handleKeyDown = (e) => {
  //   if (e.key === 'Enter') {
  //     handleAddEmail();
  //   }
  // };

  const handleRemoveEmail = (emailToRemove) => {
    setSelectedEmails(selectedEmails.filter(email => email !== emailToRemove));
  };

  const handleCheckboxChangeEmails = (email) => {
    if (!selectedEmails.includes(email)) {
      setSelectedEmails([...selectedEmails, email]);
    } else {
      setSelectedEmails(selectedEmails.filter((e) => e !== email));
    }
  };

  const handleSelectCheckedEmails = () => {
    const newSelectedEmails = Object.keys(checkedEmails).filter(email => checkedEmails[email]);
    setSelectedEmails(prevSelectedEmails => [
      ...new Set([...prevSelectedEmails, ...newSelectedEmails])
    ]); // Add checked emails to selectedEmails
    // Clear search query, dropdown options, and checked emails state
    setSearchQuery('');
    setCheckedEmails({});
    // setFilteredEmails([]);
  };

  const filteredEmails = uniqueDataEmail.filter((email) =>
    email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleManualAdd = () => {
    handleEmailChange(searchQuery2, 'dropdown2');
    setSearchQuery2(''); // Clear the search input after adding the email
  };

  const filteredManagers = Array.isArray(managers)
    ? managers.filter(manager => manager.email.toLowerCase().includes(searchQuery2.toLowerCase()))
    : [];

  const handleEmailChange1 = (email) => {
    setSelectedEmails(prevSelectedEmails => {
      let updatedEmails;
      if (prevSelectedEmails.includes(email)) {
        updatedEmails = prevSelectedEmails.filter(e => e !== email);
      } else {
        updatedEmails = [...prevSelectedEmails, email];
      }
      // Update the input field with the selected emails
      setSearchValue(updatedEmails.join(', '));
      return updatedEmails;
    });
  };

  useEffect(() => {
    // console.log("Object.keys(dashboardData)", Object.keys(dashboardData));
    if (Object.keys(dashboardData).length === 0) {
      setLoading(true);
      setStopCount(1);
      // getDashboardData();
    } else {
      fetchTableData();
      setStopCount(0);
      setLoading(false);
    }
  }, [dashboardData]);
  const [stopCount, setStopCount] = useState(0);
  // useEffect(() => {
  //   // console.log("interval useeffect")
  //   // console.log(stopCount)
  //   if (stopCount !== 0) {
  //     if (stopCount !== 0 && stopCount < 10) {
  //       const intervalId = setInterval(() => {
  //         console.log(stopCount)
  //         setStopCount(stopCount + 1)
  //       }, 1000)
  //       return () => clearInterval(intervalId)
  //     }
  //     if (stopCount === 10) {
  //       setLoading(false)
  //     }
  //   }
  // }, [stopCount])

  const [id, setId] = useState(1);
  const [loading, setLoading] = useState(true);
  //                <Hourglass
  //                   visible={true}
  //                   height="60"
  //                   width="60"
  //                   ariaLabel="hourglass-loading"
  //                   wrapperStyle={{}}
  //                   wrapperClass=""
  //                   colors={['#306cce', '#72a1ed']}
  // />
  const [countItems, setCountItems] = useState(0);
  const [searchValue, setSearchValue] = useState("");
  const [filteredId, setFilteredId] = useState([]);
  const [filteredRows, setFilteredRows] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [delId, setDelId] = useState(null);
  const location = useLocation();
  const [list, setList] = useState([]);
  // useEffect(()=>{
  //   getAllData()
  // },[])
  console
  useEffect(() => {
    // console.log("firfilteredRows essst", filteredRows);
    const closeFilterPop = (e) => {
      const allRefIds = [
        "date_ref",
        "job_ref",
        "name_ref",
        "email_ref",
        "mobile_ref",
        "client_ref",
        "profile_ref",
        "skills_ref",
        "recruiter_ref",
        "status_ref",

        "date_label_ref",
        "job_label_ref",
        "name_label_ref",
        "email_label_ref",
        "mobile_label_ref",
        "client_label_ref",
        "profile_label_ref",
        "skills_label_ref",
        "recruiter_label_ref",
        "status_label_ref",
      ];
      let bool = false;
      for (const ref of allRefIds) {
        if (document.getElementById(ref)?.contains(e.target)) {
          bool = true;
          return;
        }
      }
      if (uniRef?.current?.contains(e.target) || bool) {
        // console.log("yes");
      } else {
        // console.log("no");
        setshowSearchjobassignment((prev) => ({
          ...Object.fromEntries(Object.keys(prev).map((key) => [key, false])),
        }));
      }
    };
    document.addEventListener("click", closeFilterPop);
    return () => {
      document.removeEventListener("click", closeFilterPop);
    };
  }, []);

  useEffect(() => {
    const handleClick = (e) => {
      const target = e.target;
      const selectedItem = filteredRows?.filter((item) => {
        return (
          item.id.toString() === target.id.substring(0, target.id.length - 1)
        );
      });
      // console.log("selected item:", selectedItem);
      if (selectedItem?.length > 0) {
        const idx = list.findIndex(
          (item) => item.id === target.id.substring(0, target.id.length - 1),
        );
        let n = parseInt(target.id.substring(target.id.length - 1));
        // console.log(idx)/
        const update = new Array(filteredRows.length).fill().map((_, idx) => {
          return {
            id: filteredRows[idx].id.toString(),
            email: false,
            profile: false,
            skills: false,
            comment: false,
            // email_ref:useRef(),
            // skills_ref:useRef()
          };
        });
        update[idx] =
          n === 1
            ? {
              // id:data['candidates'][idx].id.toString(),
              ...list[idx],
              email: !list[idx].email,
              profile: false,
              skills: false,
              comment: false,
            }
            : n === 2
              ? {
                ...list[idx],
                email: false,
                profile: !list[idx].profile,
                skills: false,
                comment: false,
              }
              : n === 3
                ? {
                  ...list[idx],
                  email: false,
                  profile: false,
                  skills: !list[idx].skills,
                  comment: false,
                }
                : {
                  ...list[idx],
                  email: false,
                  profile: false,
                  skills: false,
                  comment: !list[idx].comment,
                };
        // console.log(update[ idx ])
        setList(update);
      } else {
        // console.log("filteredRows", filteredRows);
        const tempList = new Array(filteredRows?.length)
          .fill()
          .map((_, idx) => {
            return {
              id: filteredRows[idx].id.toString(),
              email: false,
              profile: false,
              skills: false,
              comment: false,
              // email_ref:useRef(),
              // skills_ref:useRef()
            };
          });
        // console.log(tempList)
        // const update = list.map(()=>({

        // }))
        if (
          target.id === "default1" ||
          target.id === "default2" ||
          target.id === "default3" ||
          target.id === "default4"
        )
          return;
        setList(tempList);
        // console.log('handle else case')
      }
    };

    window.addEventListener("click", handleClick);

    return () => {
      window.removeEventListener("click", handleClick);
    };
  });

  const handleCloseDetails = () => {
    setShowDetails(false);
  };
  const goToCandidateDetails = (candidate) => {
    // console.log("Attempting to open details for candidate:", candidate);

    // Check if candidate data is valid
    if (!candidate || typeof candidate !== 'object') {
      console.error("Invalid candidate data:", candidate);
      return;
    }

    localStorage.setItem("page_no", id); // Ensure `id` is defined and valid
    setDetails(candidate);
    setShowDetails(true);
  };
  useEffect(() => {
    console.log("Current details state:", details);
  }, [details]);

  const [showSearchjobassignment, setshowSearchjobassignment] = useState({
    showSearchName: false,
    showSearchdate: false,
    showSearchuserId: false,
    showSearchMobile: false,
    showSearchEmail: false,
    showSearchClient: false,
    showSearchProfile: false,
    showSearchSkill: false,
    showSearchRecruiter: false,
    showSearchStatus: false,
  });

  const handleOkClick = () => {
    setId(1)
    // console.log("calling handle ok click");
    updateFilteredRows({
      dateSelected,
      nameSelected,
      jobIdSelected,
      emailSelected,
      mobileSelected,
      clientSelected,
      profileSelected,
      skillSelected,
      recruiterSelected,
      statusSelected,

      setuniqueDataDate,
      setuniqueDataNames,
      setuniqueDatajobId,
      setuniqueDataEmail,
      setuniqueDataMobile,
      setuniqueDataClient,
      setuniqueDataProfile,
      setuniqueDataSkill,
      setuniqueDataRecruiter,
      setuniqueDataStatus,
    });

    setIsDateFiltered(dateSelected.length > 0);
    setIsJobIdFiltered(jobIdSelected.length > 0);
    setIsNameFiltered(nameSelected.length > 0);
    setIsEmailFiltered(emailSelected.length > 0);
    setIsMobileFiltered(mobileSelected.length > 0);
    setIsClientFiltered(clientSelected.length > 0);
    setIsProfileFiltered(profileSelected.length > 0);
    setIsSkillFiltered(skillSelected.length > 0);
    setIsRecruiterFiltered(recruiterSelected.length > 0);
    setIsStatusFiltered(statusSelected.length > 0);

    // setshowSearchjobassignment((prev) =>
    //   Object.fromEntries(
    //     Object.entries(prev).map(([key, value]) => [key, false]),
    //   ),
    // );
  };

  useEffect(() => {
    // console.log("called handlokclick");
    handleOkClick();
  }, [
    dateSelected,
    nameSelected,
    jobIdSelected,
    emailSelected,
    mobileSelected,
    clientSelected,
    profileSelected,
    skillSelected,
    recruiterSelected,
    statusSelected,
  ]);

  const handleCheckboxChangeForDate = (date_created) => {
    const isSelected = dateSelected.includes(date_created?.toLowerCase());
    if (isSelected) {
      setdateSelected((prev) => {
        // handleOkClick()
        return dateSelected.filter((d) => d !== date_created?.toLowerCase());
      });
      setSelectAllDate(false);
    } else {
      setdateSelected((prev) => {
        // handleOkClick()
        return [...dateSelected, date_created?.toLowerCase()];
      });

      setSelectAllDate(dateSelected.length === uniqueDataDate.length - 1);
    }
  };

  const handleSelectAllForDate = () => {
    const allChecked = !selectAllDate;
    setSelectAllDate(allChecked);

    if (allChecked) {
      setdateSelected((prev) => {
        // handleOkClick()
        return uniqueDataDate.map((d) => d.toString());
      });
    } else {
      setdateSelected((prev) => {
        // handleOkClick()
        return [];
      });
    }
  };
  const handleWhatsAppClick = (mobile) => {
    const url = `https://wa.me/${mobile}`;
    shell.openExternal(url);  // Open the WhatsApp link in the default browser or WhatsApp Desktop if it's installed
  };

  const handleCheckboxChange = (name) => {
    const isSelected = nameSelected.includes(name);
    if (isSelected) {
      setNameSelected((prevSelected) =>
        prevSelected.filter((item) => item !== name),
      );
      setSelectAll(false);
    } else {
      setNameSelected((prevSelected) => [...prevSelected, name]);
      setSelectAll(nameSelected.length === uniqueDataNames.length - 1);
    }
  };
  const handleSelectAllForName = () => {
    const allChecked = !selectAll;
    setSelectAll(allChecked);

    if (allChecked) {
      setNameSelected(uniqueDataNames.map((d) => d?.toLowerCase()));
    } else {
      setNameSelected([]);
    }
  };

  const handleCheckboxChangeUser = (userId) => {
    const isSelected = jobIdSelected.includes(userId);
    if (isSelected) {
      setjobIdSelected((prevSelected) =>
        prevSelected.filter((item) => item !== userId),
      );
      setselectAllForJobId(false);
    } else {
      setjobIdSelected((prevSelected) => [...prevSelected, userId]);
      setselectAllForJobId(jobIdSelected.length === uniqueDatajobId.length - 1);
    }
  };

  const handleSelectAllForUserId = () => {
    const allChecked = !selectAllForJobId;
    setselectAllForJobId(allChecked);
    if (allChecked) {
      setjobIdSelected(uniqueDatajobId.map((d) => d.toString()));
    } else {
      setjobIdSelected([]);
    }
  };

  const handleCheckboxChangeEmail = (email) => {
    const isSelected = emailSelected.includes(email);
    if (isSelected) {
      setEmailSelected((prevSelected) =>
        prevSelected.filter((item) => item !== email),
      );
      setSelectAllEmail(false);
    } else {
      setEmailSelected((prevSelected) => [...prevSelected, email]);
      setSelectAllEmail(emailSelected.length === uniqueDataEmail.length - 1);
    }
  };

  const handleSelectAllForEmail = () => {
    const allChecked = !selectAllEmail;
    setSelectAllEmail(allChecked);

    if (allChecked) {
      setEmailSelected(uniqueDataEmail.map((d) => d?.toLowerCase()));
    } else {
      setEmailSelected([]);
    }
  };

  const handleCheckBoxChangeForMobile = (mobile) => {
    const isSelected = mobileSelected.includes(mobile);
    if (isSelected) {
      setMobileSelected((prevSelected) =>
        prevSelected.filter((item) => item !== mobile),
      );
      setSelectAllMobile(false);
    } else {
      setMobileSelected((prevSelected) => [...prevSelected, mobile]);
      setSelectAllMobile(mobileSelected.length === uniqueDataMobile.length - 1);
    }
  };
  const handleSelectAllForMobile = () => {
    const allChecked = !selectAllMobile;
    setSelectAllMobile(allChecked);

    if (allChecked) {
      setMobileSelected(uniqueDataMobile.map((d) => d.toString()));
    } else {
      setMobileSelected([]);
    }
  };
  const handleCheckboxChangeClient = (client) => {
    const isSelected = clientSelected.includes(client);
    if (isSelected) {
      setclientSelected((prevSelected) =>
        prevSelected.filter((item) => item !== client),
      );
      setSelectAllClient(false);
    } else {
      setclientSelected((prevSelected) => [...prevSelected, client]);
      setSelectAllClient(clientSelected.length === uniqueDataClient.length - 1);
    }
  };
  const handleSelectAllForClient = () => {
    const allChecked = !selectAllClient;
    setSelectAllClient(allChecked);

    if (allChecked) {
      setclientSelected(uniqueDataClient.map((d) => d?.toLowerCase()));
    } else {
      setclientSelected([]);
    }
  };

  const handleCheckboxChangeProfile = (profile) => {
    const isSelected = profileSelected.includes(profile);
    if (isSelected) {
      setprofileSelected((prevSelected) =>
        prevSelected.filter((item) => item !== profile),
      );
      setSelectAllProfile(false);
    } else {
      setprofileSelected((prevSelected) => [...prevSelected, profile]);
      setSelectAllProfile(
        profileSelected.length === uniqueDataProfile.length - 1,
      );
    }
  };
  const handleSelectAllForProfile = () => {
    const allChecked = !selectAllProfile;
    setSelectAllProfile(allChecked);

    if (allChecked) {
      setprofileSelected(uniqueDataProfile.map((d) => d?.toLowerCase()));
    } else {
      setprofileSelected([]);
    }
  };

  const handleCheckboxChangeSkill = (skills) => {
    const isSelected = skillSelected.includes(skills);
    if (isSelected) {
      setskillSelected((prevSelected) =>
        prevSelected.filter((item) => item !== skills),
      );
      setSelectAllSkill(false);
    } else {
      setskillSelected((prevSelected) => [...prevSelected, skills]);
      setSelectAllSkill(skillSelected.length === uniqueDataSkill.length - 1);
    }
  };
  const handleSelectAllForSkill = () => {
    const allChecked = !selectAllSkill;
    setSelectAllSkill(allChecked);

    if (allChecked) {
      setskillSelected(uniqueDataSkill.map((d) => d?.toLowerCase()));
    } else {
      setskillSelected([]);
    }
  };
  const handleCheckboxChangeRecruiter = (recruiter) => {

    const isSelected = recruiterSelected.includes(recruiter);
    if (isSelected) {
      setrecruiterSelected((prevSelected) =>
        prevSelected.filter((item) => item !== recruiter),
      );
      setSelectAllRecruiter(false);
    } else {
      setrecruiterSelected((prevSelected) => [...prevSelected, recruiter]);
      setSelectAllRecruiter(
        recruiterSelected.length === uniqueDataRecruiter.length - 1,
      );
    }
  };
  const handleSelectAllForRecruiter = () => {
    const allChecked = !selectAllRecruiter;
    setSelectAllRecruiter(allChecked);
    if (allChecked) {
      setrecruiterSelected(uniqueDataRecruiter.map((d) => d.toLowerCase()));
    } else {
      setrecruiterSelected([]);
    }
  };
  const handleCheckboxChangeStatus = (status) => {
    const isSelected = statusSelected.includes(status);
    if (isSelected) {
      setstatusSelected((prevSelected) =>
        prevSelected.filter((item) => item !== status),
      );
      setSelectAllStatus(false);
    } else {
      setstatusSelected((prevSelected) => [...prevSelected, status]);
      setSelectAllStatus(statusSelected.length === uniqueDataStatus.length - 1);
    }
  };
  const handleSelectAllForStatus = () => {
    const allChecked = !selectAllStatus;
    setSelectAllStatus(allChecked);

    if (allChecked) {
      setstatusSelected(uniqueDataStatus.map((d) => d?.toLowerCase()));
    } else {
      setstatusSelected([]);
    }
  };

  const deleteFunction = (id) => {
    // localStorage.setItem('deleteRow',true)
    setDelId(id);
    setShowModal(true);
  };
  // @app.route('/delete_candidate/<int:candidate_id>', methods=["POST"])
  const handleDeleteCandidate = async () => {
    if (!waitForSubmission) {
      setwaitForSubmission(true);
      // console.log("delete");
      try {
        const response = await fetch(
          // `api//delete_candidate/${delId}`,{
          `http://************:5002/delete_candidate/${delId}`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              user_id: localStorage.getItem("user_id"),
            }),
          },
        );
        const data = await response.json();
        if (data.status === "success") {
          const range = getPageRange();
          if (id === range.length && filteredRows.length % 60 === 1) {
            setId((id) => id - 1);
            // console.log(id - 1);
            setBelowCount(filteredRows.length - 1);
          }
          // console.log("ok");

          deleteRow = true;
          getDashboardData().then(() => {
            setShowModal(false);
            toast.success(data.message);
            setwaitForSubmission(false);
          });
          // to get new daata after deletion
        } else {
          setwaitForSubmission(false);
          toast.error("Error occured, Please try again.");
          // console.log("delete not happened");
          // console.log(response.statusText);
        }
      } catch (err) {
        setwaitForSubmission(false);
        console.log(err);
      }
    }
  };

 const resumeApiCall = async (item) => {
  try {
    const response = await fetch(
      `http://************:5002/view_resume/${item.id}`,
      {
        method: "GET",
      }
    );

    if (response.ok) {
      const blob = await response.blob();

      // Create download URL
      const url = window.URL.createObjectURL(blob);

      // Create a link and trigger download
      const a = document.createElement("a");
      a.href = url;

      // Set filename as "CandidateName_JobRole.pdf"
      // console.log(item,"444444444444444444444444444444444444")
      const candidateName = item.name?.replace(/\s+/g, "") || "Resume";
      // console.log(item.role,"*******************************")
      const jobRole = item.profile?.replace(/\s+/g, "") || "UnknownRole";
      a.download = `${candidateName}_${jobRole}.pdf`;

      document.body.appendChild(a);
      a.click();

      // Clean up
      a.remove();
      window.URL.revokeObjectURL(url);
    } else {
      console.log("Failed to fetch resume:", response.statusText);
    }
  } catch (err) {
    console.log("Error fetching resume:", err);
  }
};

// additional file downloads

const downloadAdditionalFiles = async (item) => {
  try {
    const response = await fetch("http://************:5002/download_additional_files", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ candidate_id:item.id }),
    });

  
    const blob = await response.blob();

    // Create temporary URL
    const url = window.URL.createObjectURL(blob);

    // Create a link and trigger the download
    const a = document.createElement("a");
    a.href = url;
    a.download = `candidate_${item.name}_files.zip`;
    document.body.appendChild(a);
    a.click();

    // Cleanup
    a.remove();
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error("Error while downloading files:", error);
  }
};

  const [value, setValue] = useState(new Date());

  const handleDateChange = (date) => {
    setValue(date);
  };
  const [Error, setError] = useState();
  const [meetingDetails, setMeetingDetails] = useState([]);
  const [isSuccessful, setIsSuccessful] = useState(false);


  const syncEvents = async () => {
    try {
      const response = await fetch('http://************:5002/sync_events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          recruiter_email: localStorage.getItem('email'),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to sync events');
      }

      const data = await response.json();
      // console.log('Sync events response:', data);
    } catch (error) {
      // console.error('Sync error:', error);
      setError(error.message);
    }
  };
  const [description, setDescription] = useState('');
  const handleDescriptionChange = (e) => {
    setDescription(e.target.value);
  };
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!title || !startDate || !endDate || !startTime || !endTime || !selectedTimeZone || selectedEmails.length === 0) {
      toast.error('Please fill in all required fields.');
      return;
    }
    const requiredAttendees = selectedEmails;
    const optionalAttendees = selectedEmails2;
    if (!waitForSubmission1) {
      setwaitForSubmission1(true);
      try {
        const response = await fetch('http://************:5002/create_event', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer YOUR_ACCESS_TOKEN`,
          },
          body: JSON.stringify({
            subject: title,
            start_date: startDate,
            start_time: startTime,
            end_date: endDate,
            end_time: endTime,
            attendees: requiredAttendees,
            cc_recipients: optionalAttendees,
            time_zone: selectedTimeZone,
            recruiter_email: localStorage.getItem('email'),
            recruiter_id: localStorage.getItem("user_id"),
            description: description,
          }),
        });

        if (response.ok) {
          setModalMessage('Meeting scheduled successfully!');
          setIsModalOpen(true);
          setInterviewModal(false);
          setwaitForSubmission1(false)
          setIsSuccessful(true)
          setTitle("");
          setStartDate("");
          setEndDate("");
          setStartTime("");
          setEndTime("");
          setSelectedTimeZone("");
          setSelectedEmails([]);
          setSelectedEmails2([]);
          setDescription('');

          await syncEvents();
          await fetchMeetings();
        } else {
          setModalMessage('Failed to schedule the meeting.');
          setIsModalOpen(true);
          setwaitForSubmission1(false)
          setIsSuccessful(false)
        }
      } catch (error) {
        setModalMessage('An error occurred while scheduling the meeting.');
        setIsModalOpen(true);
        setwaitForSubmission1(false)
        setIsSuccessful(false)
        console.error('Error:', error);
      }
    }
  };



  useEffect(() => {
    const initialize = async () => {
      await syncEvents();
      fetchMeetings();
    };

    initialize();
  }, []);


  const [NonEmptyArray, setNonEmptyArray] = useState([]);
  const updateFilteredRows = ({
    dateSelected,
    nameSelected,
    jobIdSelected,
    mobileSelected,
    emailSelected,
    clientSelected,
    profileSelected,
    skillSelected,
    recruiterSelected,
    statusSelected,

    setuniqueDataDate,
    setuniqueDataNames,
    setuniqueDataMobile,
    setuniqueDatajobId,
    setuniqueDataEmail,
    setuniqueDataClient,
    setuniqueDataProfile,
    setuniqueDataSkill,
    setuniqueDataRecruiter,
    setuniqueDataStatus,
  }) => {
    let prevfilteredRows = dashboard;
    if (dateSelected.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        dateSelected.includes(row.date_created.toString()),
      );
      // console.log("ifff", dateSelected);
    }

    if (jobIdSelected.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        jobIdSelected.includes(row.job_id.toString()),
      );
      // console.log("ifff", jobIdSelected);
    }
    if (nameSelected.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        nameSelected.includes(row.name?.toLowerCase()),
      );
    }
    if (emailSelected.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        emailSelected.includes(row.email?.toLowerCase()),
      );
    }
    if (mobileSelected.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        mobileSelected.includes(row.mobile.toString()),
      );
    }
    if (clientSelected.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        clientSelected.includes(row.client?.toLowerCase()),
      );
    }
    if (profileSelected.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        profileSelected.includes(row.profile?.toLowerCase()),
      );
    }
    if (skillSelected.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        skillSelected.includes(row.skills?.toLowerCase()),
      );
    }
    if (recruiterSelected.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) => {
        const recruiter = row.recruiter;
        const normalized = typeof recruiter === "string" && recruiter.trim() !== ""
          ? recruiter.trim().toLowerCase()
          : " ";
        return recruiterSelected.includes(normalized);
      });
    }

    if (statusSelected.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        statusSelected.includes(row.status?.toLowerCase()),
      );
    }

    const arrayNames = [
      "dateSelected",
      "nameSelected",
      "jobIdSelected",
      "emailSelected",
      "mobileSelected",
      "clientSelected",
      "profileSelected",
      "skillSelected",
      "recruiterSelected",
      "statusSelected",
    ];

    const arrays = [
      dateSelected,
      nameSelected,
      jobIdSelected,
      emailSelected,
      mobileSelected,
      clientSelected,
      profileSelected,
      skillSelected,
      recruiterSelected,
      statusSelected,
    ];

    // const emptyArraysCount = arrays.filter((arr) => arr.length !== 0).length;

    let NamesOfNonEmptyArray = [];

    // if (emptyArraysCount === 1) {
    arrays.forEach((arr, index) => {
      if (arr.length > 0) {
        // NameOfNonEmptyArray = arrayNames[index];
        NamesOfNonEmptyArray.push(arrayNames[index]);
        // setNonEmptyArray(prev => ([
        //   ...prev,
        //   arrayNames[index]
        // ]))
      }
    });
    // } else if (emptyArraysCount === 0) {
    //   NameOfNonEmptyArray = null;
    // }
    // console.log("prevfilteredRows", prevfilteredRows);
    // console.log("NamesOfNonEmptyArray", NamesOfNonEmptyArray);
    setNonEmptyArray(NamesOfNonEmptyArray);
    if (!NamesOfNonEmptyArray.includes("dateSelected")) {
      setuniqueDataDate(() => {
        // console.log(
        //   "first",
        //   Array.from(
        //     new Set(
        //       prevfilteredRows.map((filteredRow) => {
        //         return "date1";
        //       }),
        //     ),
        //   ),
        // );
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.date_created;
            }),
          ),
        );
      });
    }
    if (!NamesOfNonEmptyArray.includes("jobIdSelected")) {
      setuniqueDatajobId(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.job_id;
            }),
          ),
        );
      });
    }
    if (!NamesOfNonEmptyArray.includes("nameSelected")) {
      setuniqueDataNames(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.name;
            }),
          ),
        );
      });
    }

    if (!NamesOfNonEmptyArray.includes("emailSelected")) {
      setuniqueDataEmail(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.email?.trim();
            }),
          ),
        );
      });
    }
    if (!NamesOfNonEmptyArray.includes("mobileSelected")) {
      setuniqueDataMobile(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.mobile;
            }),
          ),
        );
      });
    }

    if (!NamesOfNonEmptyArray.includes("clientSelected")) {
      setuniqueDataClient(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.client?.trim();
            }),
          ),
        );
      });
    }
    if (!NamesOfNonEmptyArray.includes("profileSelected")) {
      setuniqueDataProfile(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.profile?.trim();
            }),
          ),
        );
      });
    }
    if (!NamesOfNonEmptyArray.includes("skillSelected")) {
      setuniqueDataSkill(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.skills?.trim();
            }),
          ),
        );
      });
    }
    if (!NamesOfNonEmptyArray.includes("recruiterSelected")) {
      setuniqueDataRecruiter(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              const recruiter = filteredRow.recruiter;
              if (typeof recruiter !== "string" || recruiter.trim() === "") {
                return " "; // fallback label
              }
              return recruiter.trim();
            })
          )
        );
      });
    }

    if (!NamesOfNonEmptyArray.includes("statusSelected")) {
      setuniqueDataStatus(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.status;
            }),
          ),
        );
      });
    }
    setFilteredRows(prevfilteredRows);
    // console.log("filter of below count row",prevfilteredRows)
    if (searchValue === "") {
      setBelowCount(prevfilteredRows.length);
    }
    // console.log("it is the filter row set to the candidate:",filteredRows)
    // console.log("hi here is a change in table filter");
  };


  useEffect(() => {
    updateFilteredRows({
      dateSelected,
      nameSelected,
      jobIdSelected,
      emailSelected,
      mobileSelected,
      clientSelected,
      profileSelected,
      skillSelected,
      recruiterSelected,
      statusSelected,

      setuniqueDataDate,
      setuniqueDataNames,
      setuniqueDataMobile,
      setuniqueDatajobId,
      setuniqueDataEmail,
      setuniqueDataClient,
      setuniqueDataProfile,
      setuniqueDataSkill,
      setuniqueDataRecruiter,
      setuniqueDataStatus,
    });
  }, [nameSelected, dateSelected, jobIdSelected, emailSelected, mobileSelected, clientSelected, profileSelected, skillSelected, recruiterSelected, statusSelected, updateFilteredRows]);

//   useEffect(() => {
//   updateFilteredRows({
//     dateSelected,
//     nameSelected,
//     jobIdSelected,
//     emailSelected,
//     mobileSelected,
//     clientSelected,
//     profileSelected,
//     skillSelected,
//     recruiterSelected,
//     statusSelected,

//     setuniqueDataDate,
//     setuniqueDataNames,
//     setuniqueDataMobile,
//     setuniqueDatajobId,
//     setuniqueDataEmail,
//     setuniqueDataClient,
//     setuniqueDataProfile,
//     setuniqueDataSkill,
//     setuniqueDataRecruiter,
//     setuniqueDataStatus,
//   });
// }, [
//   nameSelected, dateSelected, jobIdSelected, emailSelected,
//   mobileSelected, clientSelected, profileSelected, skillSelected,
//   recruiterSelected, statusSelected, updateFilteredRows
// ]);


  useEffect(() => {
    if (Object.keys(dashboardData).length > 0) {
      const data = dashboardData;
      setuniqueDataDate([
        ...new Set(data["candidates"].map((d) => d.date_created)),
      ]);
      setuniqueDatajobId([...new Set(data["candidates"].map((d) => d.job_id))]);
      setuniqueDataNames([...new Set(data["candidates"].map((d) => d.name))]);

      setuniqueDataEmail([...new Set(data["candidates"].map((d) => d.email?.trim()))]);
      setuniqueDataMobile([
        ...new Set(data["candidates"].map((d) => d.mobile)),
      ]);
      setuniqueDataClient([
        ...new Set(data["candidates"].map((d) => d.client?.trim())),
      ]);
      setuniqueDataProfile([
        ...new Set(data["candidates"].map((d) => d.profile?.trim())),
      ]);
      setuniqueDataSkill([...new Set(data["candidates"].map((d) => d.skills?.trim()))]);
      //       setuniqueDataRecruiter([
      //   ...new Set(data["candidates"].map((d) => d.recruiter?.trim()).filter(Boolean))
      // ]);
      console.log("1")
      setuniqueDataRecruiter([
        ...new Set(
          data["candidates"].map((d) => d.recruiter?.trim() || " ")
        ),
      ]);


      setuniqueDataStatus([
        ...new Set(data["candidates"].map((d) => d.status)),
      ]);
    }
  }, [dashboardData]);
  // console.log("uniqueDataRecruiter", uniqueDataRecruiter);
  useEffect(() => {
    setBelowCount(dashboard?.length);
    setFilteredRows(dashboard);
  }, [dashboard]);

  const dis1 = (data) => {
    // console.log(data);
    if (data?.length > 60) {
      const updatedData = data.filter(
        (_, idx) => idx + 1 <= id * 60 && idx + 1 > (id - 1) * 60,
      );
      return updatedData;
    } else {
      return data;
    }
  };

  const [isColorFiltered, setIsColorFiltered] = useState(false);

  const [selectedColors, setselectedColors] = useState([false, false, false])

  // Sorting state
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });

  const displayItems = () => {
    // console.log('filteredRows:',filteredRows)
    const data = filteredRows?.filter((item) => {
      if (filteredId.length > 0) {
        for (const it of filteredId) {
          if (it === item.id) {
            return true;
          }
        }
        // Return false only if none of the elements in filteredId match item.id
        return false;
      } else {
        if (searchValue === "") return true;
        else return false;
      }

    });
    const data1 = dis1(data);

    // console.log(data)
    // setShowData(data)
    // if(data?.length>0)
    //   return data;
    // commonFun({...allData,candidates:data})
    return data1;
  };

  const handleSelectedColors = (index) => {
    setId(1)
    setselectedColors(prev => {
      let updatedColors = [...prev]
      updatedColors[index] = !updatedColors[index]

      const anyColorSelected = updatedColors.some(color => color);
      setIsColorFiltered(anyColorSelected);
      // setShowCandidates(prevCand => {
      //   if(new Set(updatedColors).size === 1){
      //     return prevCand
      //   }
      //   console.log("prevCand", prevCand)
      //   console.log("updatedColors", updatedColors)
      //   let filteredCand = prevCand.filter(cand => {
      //     if(updatedColors[0] && (cand.status.toLowerCase().includes("selected") || cand.status.toLowerCase().includes("boarded") )){
      //       return true
      //     }
      //     if(updatedColors[2] && (cand.status.toLowerCase().includes("rejected") || cand.status.toLowerCase().includes("hold") || cand.status.toLowerCase().includes("drop") || cand.status.toLowerCase().includes("show") || cand.status.toLowerCase().includes("duplicate") )){
      //       return true
      //     }if (updatedColors[1] && (!cand.status.toLowerCase().includes("selected") && !cand.status.toLowerCase().includes("boarded") && !cand.status.toLowerCase().includes("rejected") && !cand.status.toLowerCase().includes("hold") && !cand.status.toLowerCase().includes("drop") && !cand.status.toLowerCase().includes("show") && !cand.status.toLowerCase().includes("duplicate")  )){
      //       return true
      //     }
      //     return false
      //   })
      //   console.log("filteredCand", filteredCand)
      //   return filteredCand;
      // })
      return updatedColors
    })
  }
  const notify = () => toast.success("candidate added successfully");
  const notifyDelete = () => toast.success("candidate deleted successfully");

  // Sorting function
  // const handleSort = (key) => {
  //   let direction = 'asc';
  //   if (sortConfig.key === key && sortConfig.direction === 'asc') {
  //     direction = 'desc';
  //   }
  //   setSortConfig({ key, direction });
  //   setId(1); // Reset to first page when sorting
  // };
// const handleSort = (key) => {
//   setSortConfig({ key, direction: 'asc' }); // Always sort ascending
//   setId(1); // Reset to first page when sorting
// };

const handleSort = (key) => {
  if (sortConfig.key === key && sortConfig.direction === 'asc') {
    // If already sorted ascending, remove sorting
    setSortConfig({ key: null, direction: null });
  } else {
    // Otherwise, sort ascending
    setSortConfig({ key, direction: 'asc' });
  }
  setId(1); // Reset pagination
};

  // Get sort icon based on current sort state


const getSortIcon = (columnKey) => {
  const isActive = sortConfig.key === columnKey;
  const rotation = sortConfig.direction === 'desc' ? '180deg' : '0deg';


  

  return (
    <BiSort
      className="sort-icon"
      style={{
        fontSize: '16px',
        marginLeft: '5px',
        fontWeight: '800',
        color: isActive ? 'orange' : '#fff',
        transform: isActive ? `rotate(${rotation})` : 'none',
        transition: 'transform 0.2s ease',
      }}
    />
  );
};


  // Apply sorting to items
  const sortedItems = useMemo(() => {
    const itemsToSort = displayItems();
    if (!sortConfig.key || !itemsToSort) return itemsToSort;

    return [...itemsToSort].sort((a, b) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];

      // Handle null/undefined values
      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return sortConfig.direction === 'asc' ? 1 : -1;
      if (bValue == null) return sortConfig.direction === 'asc' ? -1 : 1;

      // Handle different data types
      let comparison = 0;

      if (sortConfig.key === 'date_created') {
        // Sort dates
        comparison = new Date(aValue) - new Date(bValue);
      } else if (sortConfig.key === 'job_id' || sortConfig.key === 'mobile') {
        // Sort numbers
        comparison = Number(aValue) - Number(bValue);
      } else {
        // Sort strings (case insensitive)
        const aStr = String(aValue).toLowerCase();
        const bStr = String(bValue).toLowerCase();
        comparison = aStr.localeCompare(bStr);
      }

      return sortConfig.direction === 'asc' ? comparison : -comparison;
    });
  }, [displayItems, sortConfig]);

  const items = sortedItems;
  // console.log("dashboard sasva",items)
  // const

  const fetchTableData = () => {
    try {
      const data = dashboardData;
      // console.log("dashboardData", data);
      setDashboard(data["candidates"]);
      // commonFun(data);
      setAllData(data);
      // setLoading(false);
      const tempList = new Array(data["candidates"].length)
        .fill()
        .map((_, idx) => {
          return {
            id: data["candidates"][idx].id.toString(),
            email: false,
            profile: false,
            skills: false,
            comment: false,
            // email_ref:useRef(),
            // skills_ref:useRef()
          };
        });
      // console.log(tempList, 'templist')
      setList(tempList);
      if (!data.candidates) {
        throw new Error("Expected 'candidates' property not found");
      }

      return data;
    } catch (err) {
      // console.error("Error fetching data:", err);
      return null; // Return null in case of an error
    }
  };

  // navigate("/UpdateCandidate", { state: { item, path: location.pathname } });
  const goToEdit = async (id) => {
    const data = dashboardData;
    // console.log(data);
    const sendData = data["candidates"].filter((item) => item.id === id);
    localStorage.setItem("isUpdated", false);
    navigate("/EditCandidate", {
      state: { item: sendData[0], path: location.pathname },
    });
  };

  // useEffect(()=>{
  //   console.log('id changed',id)
  //   setShowData(displayItems())
  //   console.log('done')
  // },[id])

  useEffect(() => {
    // console.log(location.state, "state");

    if (location.state != undefined) {
      // console.log("Name from location.state:", location.state?.name);
      localStorage.setItem("user_id", location.state?.user_id);
      localStorage.setItem("user_type", location.state?.user_type);
      localStorage.setItem("user_name", location.state?.user_name);
      localStorage.setItem("name", location.state?.name);


    }
  }, []);

  // useEffect(() => {
  //   // console.log("filteredRows changed", filteredRows);
  // }, [filteredRows]);



  function fun(data) {
    // console.log("calling fun ");
    // console.log(data);
    // setBelowCount(data['candidates'].length)
    // if(Object.keys(dashboardData).length > 0){
    const tempList = new Array(data["candidates"].length)
      .fill()
      .map((_, idx) => {
        return {
          id: data["candidates"][idx].id.toString(),
          email: false,
          profile: false,
          skills: false,
          comment: false,
          // email_ref:useRef(),
          // skills_ref:useRef()
        };
      });
    const list = data["candidates"].filter((it) => {
      return filteredRows.some((item) => item.id === it.id);
    });
    setBelowCount(list?.length)
    if (searchValue === "") {
      if (localStorage.getItem("page_no")) {
        setId(localStorage.getItem("page_no"));
        localStorage.removeItem("page_no");
      }
    } else {
      setId(1);
    }
    // console.log(list);
    // setuniqueDataDate([...new Set(list.map((d) => d.date_created))]);
    // setuniqueDatajobId([...new Set(list.map((d) => d.job_id))]);
    // setuniqueDataNames([...new Set(list.map((d) => d.name))]);

    // setuniqueDataEmail([...new Set(list.map((d) => d.email))]);
    // setuniqueDataMobile([...new Set(list.map((d) => d.mobile))]);
    // setuniqueDataClient([...new Set(list.map((d) => d.client))]);
    // setuniqueDataProfile([...new Set(list.map((d) => d.profile))]);
    // setuniqueDataSkill([...new Set(list.map((d) => d.skills))]);
    // setuniqueDataRecruiter([...new Set(list.map((d) => d.recruiter))]);
    // setuniqueDataStatus([...new Set(list.map((d) => d.status))]);
    // console.log(tempList, "templist");
    setList(tempList);
    // }

    // setLoading(false);

    // console.log('imp  ')
    // setDashboard(data["candidates"]); // Set dashboard data

    // let val = data[ "candidates" ].length;
    // if (val % 60 != 0)
    //   setCountItems(parseInt(val / 60) + 1)
    // else
    //   setCountItems(parseInt(val / 60))

    // console.log(countItems)
    // console.log("dashboarddatas", data)
    // Set unique data for filters
    // console.log(filteredRows);
    // console.log(data["candidates"]);
  }

  // function commonFun(data) {
  //   // console.log(data);
  //   // if(Object.keys(dashboardData).length > 0){
  //   const tempList = new Array(data["candidates"].length)
  //     .fill()
  //     .map((_, idx) => {
  //       return {
  //         id: data["candidates"][idx].id.toString(),
  //         email: false,
  //         profile: false,
  //         skills: false,
  //         // email_ref:useRef(),
  //         // skills_ref:useRef()
  //       };
  //     });
  //   // console.log(tempList, "templist");
  //   setList(tempList);
  //   // }
  //   setLoading(false);
  //   // console.log("imp  ");
  //   setDashboard(data["candidates"]); // Set dashboard data
  //   let val = data["candidates"].length;
  //   if (val % 60 != 0) setCountItems(parseInt(val / 60) + 1);
  //   else setCountItems(parseInt(val / 60));
  //   // console.log(countItems)
  //   console.log("dashboarddatas", data)
  //   // Set unique data for filters
  //   console.log("[...new Set(data[candidates].map((d) => d.date_created))]", [...new Set(data["candidates"].map((d) => d.date_created))])
  //   setuniqueDataDate([...new Set(data["candidates"].map((d) => d.date_created))]);
  //   setuniqueDatajobId([...new Set(data["candidates"].map((d) => d.job_id))]);
  //   setuniqueDataNames([...new Set(data["candidates"].map((d) => d.name))]);

  //   setuniqueDataEmail([...new Set(data["candidates"].map((d) => d.email))]);
  //   setuniqueDataMobile([...new Set(data["candidates"].map((d) => d.mobile))]);
  //   setuniqueDataClient([...new Set(data["candidates"].map((d) => d.client))]);
  //   setuniqueDataProfile([...new Set(data["candidates"].map((d) => d.profile))]);
  //   setuniqueDataSkill([...new Set(data["candidates"].map((d) => d.skills))]);
  //   setuniqueDataRecruiter([
  //     ...new Set(data["candidates"].map((d) => d.recruiter)),
  //   ]);
  //   setuniqueDataStatus([...new Set(data["candidates"].map((d) => d.status))]);
  // }

  const handleCloseModal = () => {
    setShowModal(false);
  };
  const goToPage = (pageNumber) => {
    if (pageNumber >= 1 && pageNumber <= countItems) {
      setId(pageNumber);
    }
  };

  // Calculate the range of pages to display in pagination
  const getPageRange = () => {
    const pageRange = [];
    const maxPagesToShow = 5; // Adjust this value to show more or fewer page numbers

    let startPage = Math.max(1, id - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(countItems, startPage + maxPagesToShow - 1);

    // Adjust startPage and endPage if near the beginning or end
    if (endPage - startPage < maxPagesToShow - 1) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    // Include ellipsis if necessary
    if (startPage > 1) {
      pageRange.push(1);
      if (startPage > 2) {
        pageRange.push("...");
      }
    }

    // Add page numbers to the range
    for (let i = startPage; i <= endPage; i++) {
      pageRange.push(i);
    }

    // Include ellipsis if necessary
    if (endPage < countItems) {
      if (endPage < countItems - 1) {
        pageRange.push("...");
      }
      pageRange.push(countItems);
    }

    return pageRange;
  };
  function extractKeyValuePairs(object, keysToExtract) {
    return keysToExtract.reduce((acc, key) => {
      if (key in object) {
        acc[key] = object[key];
      }
      return acc;
    }, {});
  }

  const removeAllFilter = () => {
    setdateSelected([])
    setjobIdSelected([])
    setNameSelected([])
    setEmailSelected([])
    setMobileSelected([])
    setclientSelected([])
    setprofileSelected([])
    setskillSelected([])
    setrecruiterSelected([])
    setstatusSelected([])
    setselectedColors([false, false, false])
    setIsColorFiltered(false)

    setSelectAllClient([])
  }


  useEffect(() => {
    // console.log(searchValue)
    if (dashboard?.length > 0) {
      // console.log(dashboard);
      const update = dashboard.filter((item) => {
        const extractedObj = extractKeyValuePairs(item, [
          "id",
          "date_created",
          "job_id",
          "name",
          "email",
          "mobile",
          "client",
          "profile",
          "skills",
          "recruiter",
          "status",
        ]);
        // console.log(extractedObj)
        for (const key in extractedObj) {
          if (key === "id") {
            continue;
          }
          // console.log('key',key)
          let val = extractedObj[key];
          // console.log(val) 
          if (val !== null && val !== undefined) {
            if (typeof val !== "string") {
              val = val.toString();
            }
            if (val.toLowerCase().includes(searchValue?.toLowerCase())) {
              // console.log('yes working good')
              return true;
            }
          }
        }
        // console.log('No match found for searchValue:', searchValue);
        return false;
      });

      const originalFilteredCandidates = update;
      const filteredByColor = new Set(selectedColors).size === 1
        ? originalFilteredCandidates
        : originalFilteredCandidates.filter(cand => {
          if (selectedColors[0] && (cand.status.toLowerCase().includes("selected") || cand.status.toLowerCase().includes("boarded"))) {
            return true;
          }
          if (selectedColors[2] && (cand.status.toLowerCase().includes("rejected") || cand.status.toLowerCase().includes("hold") || cand.status.toLowerCase().includes("drop") || cand.status.toLowerCase().includes("show") || cand.status.toLowerCase().includes("duplicate"))) {
            return true;
          }
          if (selectedColors[1] && (!cand.status.toLowerCase().includes("selected") && !cand.status.toLowerCase().includes("boarded") && !cand.status.toLowerCase().includes("rejected") && !cand.status.toLowerCase().includes("hold") && !cand.status.toLowerCase().includes("drop") && !cand.status.toLowerCase().includes("show") && !cand.status.toLowerCase().includes("duplicate"))) {
            return true;
          }
          return false;
        });

      fun({ ...allData, candidates: filteredByColor });
      let extract = [];
      for (const item of filteredByColor) {
        extract.push(item.id);
      }
      setFilteredId(extract);
      // console.log(extract);
    }
  }, [selectedColors, filteredRows, searchValue]);



  // useEffect(()=>{
  //   console.log(mostUpdatedFilter)
  //   setFilteredRows(mostUpdatedFilter)
  // },[mostUpdatedFilter])
  // useEffect(() => {
  //   // console.log(filteredRows);
  //   // console.log(dashboard);
  // }, [filteredRows, dashboard]);
  // useEffect(()=>{
  //   // console.log(filteredRows)
  //   commonFun(filteredRows)
  // },[searchValue])

  useEffect(() => {
    const fun = async () => {
      if (localStorage.getItem("page_no")) {
        const page = parseInt(localStorage.getItem("page_no"));
        setId(page);
        localStorage.removeItem("page_no");
      }
    };
    fun();
  }, [filteredRows]);

  // useEffect(() => { }, []);

  useEffect(() => {
    // console.log(belowCount, "fggdgrtewgrgwrfwefwefwfw")
    if (belowCount % 60 != 0) setCountItems(parseInt(belowCount / 60) + 1);
    else setCountItems(parseInt(belowCount / 60));
  }, [belowCount]);



  const statusColorMapping = {
    "selected": "green",
    "boarded": "green",
    "rejected": "red",
    "hold": "red",
    "drop": "red",
    "duplicate": "red",
    "show": "red",
  };
  const getStatusColor = (status) => {
    const statusLowerCase = status.toLowerCase();
    if (statusColorMapping.hasOwnProperty(statusLowerCase)) {
      return statusColorMapping[statusLowerCase];
    }
    if (statusLowerCase.includes("selected") ||
      statusLowerCase.includes("boarded")) {
      return "green";
    } else if (
      statusLowerCase.includes("rejected") ||
      statusLowerCase.includes("hold") ||
      statusLowerCase.includes("drop") ||
      statusLowerCase.includes("duplicate") ||
      statusLowerCase.includes("show")
    ) {
      return "red";
    } else {
      return "orange";
    }
  };
  const timeOptions = Array.from({ length: 24 * 2 }, (_, i) => {
    const hour = Math.floor(i / 2);
    const minute = (i % 2) * 30;
    return {
      value: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
      label: `${hour}:${minute.toString().padStart(2, '0')}`
    };
  });

  const add30Minutes = (time) => {
    const [hours, minutes] = time.split(':').map(Number);
    let newMinutes = minutes + 30;
    let newHours = hours;

    if (newMinutes >= 60) {
      newMinutes -= 60;
      newHours += 1;
    }

    if (newHours === 24) {
      newHours = 0;
    }

    return `${String(newHours).padStart(2, '0')}:${String(newMinutes).padStart(2, '0')}`;
  };

  useEffect(() => {
    if (startTime) {
      setEndTime(add30Minutes(startTime));
    } else {
      setEndTime('');
    }
  }, [startTime]);
  const [selectedFields, setSelectedFields] = useState([]);
  const [showEmailModal, setShowEmailModal] = useState(false);
  // const [recipientEmail, setRecipientEmail] = useState('');
  const closeModal = () => {
    setShowEmailModal(false);
    setSelectedEmails2([]);
  };
  const toggleField = (field) => {
    setSelectedFields((prev) =>
      prev.includes(field) ? prev.filter(f => f !== field) : [...prev, field]
    );
  };

  // const handleEmailShare = () => {
  //   const shareDetails = selectedFields.map(field => `
  //     <tr>
  //       <th style="background-color: green; color: white; padding: 8px;">${field}:</th>
  //       <td style="background-color: green; color: white; padding: 8px;">${details[field]}</td>
  //     </tr>
  //   `).join("");

  //   const emailBody = `
  //     <html>
  //       <body>
  //         <h3 style="text-align: center;">Candidate Details</h3>
  //         <table style="border-collapse: collapse; width: 100%;">
  //           ${shareDetails}
  //         </table>
  //       </body>
  //     </html>
  //   `;


  //   const plainTextBody = selectedFields.map(field => `${field}: ${details[field]}`).join("\n");

  //   const mailtoBody = encodeURIComponent(plainTextBody);
  //   const mailtoLink = `mailto:?subject=Candidate Details&body=${mailtoBody}`;


  //   window.location.href = mailtoLink;
  // };
  const [selectedStoreddata, setselectedStoreddata] = useState()
  const handleSelectField = (jobId, fieldName, value) => {
    setSelectedFields((prevSelectedFields) => {
      const updatedSelectedFields = { ...prevSelectedFields };

      // Check if the field is selected globally (for all jobs)
      if (updatedSelectedFields[fieldName]) {
        // If it is selected, we need to deselect it (remove from all jobs)
        delete updatedSelectedFields[fieldName];  // Deselect globally

        // Deselect for all individual jobs
        selectedJobDetails.forEach((job) => {
          delete updatedSelectedFields[job.id]?.[fieldName];
        });
      } else {
        // Otherwise, globally select this field and update it for all jobs
        updatedSelectedFields[fieldName] = value || "No Value"; // Default to "No Value" for empty values

        // Update all jobs with the same value for this field
        selectedJobDetails.forEach((job) => {
          if (!updatedSelectedFields[job.id]) {
            updatedSelectedFields[job.id] = {};
          }
          updatedSelectedFields[job.id][fieldName] = value || "No Value";  // Handle empty value
        });
      }

      // console.log('Updated Selected Fields:', updatedSelectedFields);
      return updatedSelectedFields;
    });
  };
  const handleUnselectJob = (jobId) => {
    // Update selectedJobDetails by filtering out the unselected job
    const updatedJobDetails = selectedJobDetails.filter(job => job.id !== jobId);
    setSelectedJobDetails(updatedJobDetails);

    // Update selectedJobs to remove the deselected job id
    setSelectedJobs((prevSelectedJobs) => prevSelectedJobs.filter((id) => id !== jobId));
  };









  const handleShareEmailClick = () => {
    setShowEmailModal(true);
    setShowDetails(true);

    const selectedFieldValues = selectedFields.reduce((acc, field) => {
      acc[field] = details[field]; // Assuming details contains the values for these fields
      return acc;
    }, {});
    setselectedStoreddata(selectedFieldValues)

    // console.log('Selected Fields:', selectedFieldValues);
  };

  // const handleEmailSend = () => {
  //   // Add your email sending logic here
  //   console.log(`Sending email to: ${recipientEmail}`);
  //   // Reset the email input and close the modal
  //   setRecipientEmail('');
  //   setShowEmailModal(false);
  //   setSelectedEmails2([])

  //   // Optionally, invoke handleEmailShare() with the recipientEmail if needed
  // };
  // const [searchQuerys, setSearchQuerys] = useState("");
  // const [filteredEmails1, setFilteredEmails1] = useState([]);
  // const [isEmailAdded, setIsEmailAdded] = useState(false);
  // useEffect(() => {
  //   // Filter emails based on the search query
  //   if (searchQuerys) {
  //     const lowercasedQuery = searchQuerys.toLowerCase();
  //     const filtered = allEmails.filter(email => email.toLowerCase().includes(lowercasedQuery));
  //     setFilteredEmails1(filtered);
  //   } else {
  //     setFilteredEmails1([]);
  //   }
  // }, [searchQuerys, allEmails]);
  // const handleAddemail = () => {
  //   setIsEmailAdded(true);
  //   setSearchQuerys(searchQuerys);
  //   // Clear the filtered emails after adding
  //   setFilteredEmails1([]);
  // };

  useEffect(() => {
    if (showEmailModal) {
      setSearchQuery2('');
      inputRef2.current;
    }
  }, [showEmailModal]);

  const handleEmailChanges = (email) => {
    setSelectedEmails2((prev) => {
      if (prev.includes(email)) {
        return prev.filter((e) => e !== email);
      }
      return [...prev, email];
    });
    setSearchQuery2('')
  };

  const [showEmailpasswordModal, setshowEmailpasswordModal] = useState(false);
  const [Emailpassword, setEmailpassword] = useState([]);
  const [emailSubject, setEmailSubject] = useState('');
  const [emailBody, setEmailBody] = useState('');
  const [showNewPassword, setShowNewPassword] = useState(false);
  const closepasswordModal = () => {
    setshowEmailpasswordModal(false);
    setEmailpassword('');
    setSelectedEmails2([]);
    setEmailSubject("")
    setEmailBody("")

  }

  const handleEmailSend = () => {

    setshowEmailpasswordModal(true)
    setShowEmailModal(false)
    setShowModals(false)

  };
  const [waitForSubmissionemail, setwaitForSubmissionemail] = useState(false);
  const handleEmailSendconfrim = async () => {
    if (!waitForSubmissionemail) {
      setwaitForSubmissionemail(true);

      const localStorageEmail = localStorage.getItem('email');

      // Prepare selectedJobDetails
      const selectedDetails = selectedJobDetails.map((job) => {
        let selectedJobFields = selectedFields[job.id];
        // console.log(`Selected fields for job ${job.id}:`, selectedJobFields);

        if (selectedJobFields && typeof selectedJobFields === 'object' && !Array.isArray(selectedJobFields)) {
          selectedJobFields = Object.keys(selectedJobFields); // Extract field names from object
        }
        if (!Array.isArray(selectedJobFields)) {
          selectedJobFields = [];
        }

        const jobDetails = {};
        selectedJobFields.forEach((field) => {
          // Ensure empty values are explicitly set to " - "
          jobDetails[field] = job[field] || " - ";
        });

        return { jobDetails };
      });

      // console.log('Selected Details (Selected Fields with Values):', selectedDetails);

      // Include selectedCandidate in the data payload
      // console.log('Selected Candidates (Name and ID):', selectedCandidate);

      const recipientEmails = selectedEmails2.join(', ');
      const localname = localStorage.getItem('name')

      const secretKey = "ATS@mako";
      const encryptedPassword = CryptoJS.AES.encrypt(
        Emailpassword,
        secretKey
      ).toString();
      const data = {
        selectedDetails,
        selectedCandidate, // Include selectedCandidate in the payload
        localStorageEmail,
        recipientEmails,
        emailSubject,
        encryptedPassword,
        emailBody,
        localname,
      };

      try {
        const response = await fetch('http://************:5002/selected_details_candidate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });

        const result = await response.json();
        if (response.ok) {
          setwaitForSubmissionemail(false);
          // console.log('Success foremail:', result);
          toast.success("Authentication successful");
          toast.success(result.message);
          setShowEmailModal(false);
          setshowEmailpasswordModal(false);
          fetchsendclient()
          setSelectedJobs([]);
          setSelectedFields([]);
          setSelectedJobDetails([]);
          // Clear selectedCandidate state
          setEmailpassword('');
          setSearchQuery2('');
          setSelectedEmails2([]);
          setEmailSubject("");
          setEmailBody("");
        } else {
          setwaitForSubmissionemail(false);
          console.error('Error:', result.message);
          toast.error(result.message);
        }
      } catch (error) {
        console.error('Error:', error);
        setwaitForSubmissionemail(false);
        toast.error('Something went wrong, please try again');
      }
    }
  };
  const fetchsendclient = async () => {




    // Prepare selectedJobDetails


    // console.log('Selected Details (Selected Fields with Values):', selectedDetails);

    // Include selectedCandidate in the data payload
    // console.log('Selected Candidates (Name and ID):', selectedCandidate);
    const recruiterEmail = localStorage.getItem('email');
    const recruiterName = localStorage.getItem('name')
    const data = {

      selectedCandidate, // Include selectedCandidate in the payload
      recruiterEmail,
      recruiterName
    };

    try {
      const response = await fetch('http://************:5002/match_candidates_to_jobs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();
      if (response.ok) {

        // console.log('Success foremail:', result);
        // toast.success("Authentication successful");
        toast.success(result.message);
        setShowEmailModal(false);
        setshowEmailpasswordModal(false);
        setSelectedJobs([]);
        setSelectedFields([]);
        setSelectedJobDetails([]);
        setSelectedCandidate([]); // Clear selectedCandidate state
        setEmailpassword('');
        setSearchQuery2('');
        setSelectedEmails2([]);
        setEmailSubject("");
        setEmailBody("");
      } else {

        console.error('Error:', result.message);
        // toast.error(result.message);
      }
    } catch (error) {
      console.error('Error:', error);

      //  toast.error('Something went wrong, please try again');
    }
  };

  const togglePasswordVisibility = (field) => {
    if (field === "newPassword") {
      setShowNewPassword((prev) => !prev);
    }
  };

  const [selectedJobs, setSelectedJobs] = useState([]);
  const [selectedCandidate, setSelectedCandidate] = useState([]);
  const [showModals, setShowModals] = useState(false);
  const [selectedJobDetails, setSelectedJobDetails] = useState([]);
  // const [currentJobId, setCurrentJobId] = useState(null);
  const [currentClient, setCurrentClient] = useState(null);
  useEffect(() => {
    if (selectedJobs.length === 0) {
      // console.log("All jobs deselected. Resetting currentClient to null.");
      setCurrentClient(null);
    }
  }, [selectedJobs]); // Runs when selectedJobs changes
  const handleCheckboxChangeShare = (candidate) => {
    const isSelected = selectedShares.includes(candidate.id);

    if (isSelected) {
      // Deselect the candidate
      const updatedShares = selectedShares.filter((id) => id !== candidate.id);
      setSelectedShares(updatedShares);

      // Reset selected client if no candidates are selected
      if (updatedShares.length === 0) {
        setSelectAllClient(null);
      }
    } else {
      // Check if the candidate's client matches the selected client (or no client is selected yet)
      if (!selectAllClient || selectAllClient === candidate.client) {
        setSelectedShares((prev) => [...prev, candidate.id]);
        setSelectAllClient(candidate.client);
      }
    }
  };
  const toggleRowSelection = (candidate) => {
    // console.log("Before updating state:");
    // console.log("selectedJobs:", selectedJobs);
    // console.log("currentClient:", currentClient);

    // If no client is set, initialize with the current candidate's client
    if (currentClient === null) {
      setCurrentClient(candidate.client);
      setSelectedJobs((prevSelectedJobs) => [...prevSelectedJobs, candidate.id]);
    }  else {
      setSelectedJobs((prevSelectedJobs) => {
        const isSelected = prevSelectedJobs.includes(candidate.id);
        const newSelectedJobs = isSelected
          ? prevSelectedJobs.filter((itemId) => itemId !== candidate.id)
          : [...prevSelectedJobs, candidate.id];

        // If no jobs remain selected, reset currentClient
        if (newSelectedJobs.length === 0) {
          setCurrentClient(null);
        }

        return newSelectedJobs;
      });
    }

    // console.log("After updating state:");
    // console.log("selectedJobs:", selectedJobs);
    // console.log("currentClient:", currentClient);
  };



  const handleCheckboxClick = (candidate) => {
    toggleRowSelection(candidate); // This will toggle the selection when checkbox is clicked
  };






  // // UseEffect to debug the currentClient value
  // useEffect(() => {
  //   console.log("Current Client updated:", currentClient); // Log whenever currentClient changes
  // }, [currentClient]);





  // Open modal to view all selected candidates
  const newopenModal = () => {
    // console.log('Selected Jobs:', selectedJobs);

    const selectedDetails = dashboard.filter((item) => selectedJobs.includes(item.id));
    // console.log('Selected candidate Details:', selectedDetails);

    // Extract name and id for selected candidates
    const selectedCandidates = selectedDetails.map((candidate) => ({
      id: candidate.id,
      name: candidate.name,
    }));

    // console.log('Selected Candidates (Name and ID):', selectedCandidates);

    // Logging selected fields for each job
    const selectedFieldsForJobs = selectedDetails.map((job) => {
      const selectedFieldsForJob = selectedFields[job.id] || [];
      // console.log(`Selected Fields for Job ${job.job_id}:`, selectedFieldsForJob);
      return {
        job_id: job.job_id,
        selected_fields: selectedFieldsForJob,
      };
    });
    // console.log('Selected Fields for All Jobs:', selectedFieldsForJobs);

    // Set the extracted details into separate state
    setSelectedCandidate(selectedCandidates);

    setSelectedJobDetails(selectedDetails);
    setShowModals(true);
  };




  // Close modal
  const newcloseModal = () => {
    setShowModals(false);
    setSelectedJobs([]);
    setSelectedFields([]);
    setSelectedJobDetails([])
  };

  const handleDownload = () => {
    // console.log(dashboardData, "bfjgbjdfbgj");

    // Check if dashboardData exists and contains the expected user and candidates properties
    // console.log("handle codes of the system",items)
    // const isFilterApplied =  searchValue !== "";

    // Use filtered data if filters are applied, otherwise use full dashboard data
    const isFilterApplied = searchValue !== "";

    // Use `filteredRows` if a search value exists, otherwise use `items`
    const candidates = isFilterApplied ? items : filteredRows;

    if (candidates.length === 0) {
      console.error("No candidates data available");
      return;
    }
    // const candidates = items;
    // console.log("handle codes of the system",candidates)
    const candidatesWithSerialNumber = candidates.map((candidate, index) => ({
      "S.No": index + 1, // Serial number starts from 1
      ...candidate,       // Spread the existing candidate data
    }));

    // console.log(candidatesWithSerialNumber, "bshdbf");


    // Create a workbook and worksheet from the candidates data
    const ws = XLSX.utils.json_to_sheet(candidatesWithSerialNumber);

    // Set the file name for the Excel file
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Candidates Data');

    // Export the Excel file
    XLSX.writeFile(wb, 'candidates_data.xlsx');
  };

  const [isAnimating, setIsAnimating] = useState(false);


  useEffect(() => {
    setIsAnimating(true);
    setTimeout(() => {
      setIsAnimating(false);
    }, 500); // Adjust the timeout to match your animation duration
  }, [location]);


  useEffect(() => {
    const handleMouseMove = (e) => {
      const ele = document.getElementById('scrollable-element');
      if (!ele) return;
      const distance = ele.offsetLeft + ele.offsetWidth - e.pageX;
      if (distance < 15 && distance > -15) {
        ele.classList.add('more-width');
      } else {
        ele.classList.remove('more-width');
      }
    };

    document.addEventListener('mousemove', handleMouseMove);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
    };
  }, []);

  const [visibleCommentId, setVisibleCommentId] = useState(null);

  // const fetchComment = async (id) => {

  //   try {

  //     const response = await fetch(
  //       `http://************:5002/update_comment_status`,
  //       {
  //         method: "POST",
  //         headers: {
  //           "Content-Type": "application/json",
  //         },
  //         body: JSON.stringify({
  //           id: id
  //         }),
  //       },
  //     );

  //     const data = await response.json();
  //     if (!response.ok) {
  //       throw new Error("Failed to update candidate");
  //     }

  //     getDashboardData().then(() => {
  //       setwaitForSubmission(false);
  //       // toast.success(data.message);
  //       navigate("/dashboard");
  //     });
  //   } catch (error) {
  //     console.error("Error fetching comment:", error);
  //   }
  // };


  return (
    <div className="wrapper">
      <LeftNav ref={leftNavRef} />
      <div className="section">
        <TitleBar />
        {/* {loading ? (
          <div className="loader-container">
            <Hourglass
              // visible={true}
              height="60"
              width="340"
              ariaLabel="hourglass-loading"
              wrapperStyle={{}}
              wrapperClass=""
              colors={["#306cce", "#72a1ed"]}
            />
          </div>
        ) : null ( */}
        {/* <div style={{
          marginTop: "40px", height: "80vh", display: "flex",
          justifyContent: "center", alignItems: "center"
        }}> */}
        {/* <div style={{ display: "flex", flexDirection: "column", backgroundColor: "lightgray", padding: "10px 10px 10px 10px", borderRadius: "4px", rowGap: "0px" }}>
            <img style={{ height: "100px", width: "100px" }} src={Error} alt="error" />
            <h3 style={{ paddingBottom: "0px" }}>Something went wrong</h3>
            <text style={{ fontWeight: 300 }}>Try refreshing the page, or try again later.</text>
            <div style={{ display: "flex" }}>
              <motion.button
                className="error-refresh"
                id={"addCandidateSubmit"}
                type="submit"
                style={{
                  borderRadius: "4px",
                  background: "#32406D",
                  color: "#fff",
                  width: "100px",
                  position: "relative",
                }}
                onClick={() => {
                  window.location.reload()
                  setshowRadio(true)
                }}
                whileTap={{ scale: 0.8 }} // Scale down to 80% of the original size on click
                animate={{ scale: 1 }} // Animate back to original size
                transition={{ duration: 0.2 }} // Duration for the animation
              >
                Refresh Page
              </motion.button>
              <div>
                <Radio
                  visible={showRadio}
                  height="40"
                  width="40"
                  color="#4fa94d"
                  ariaLabel="radio-loading"
                  wrapperStyle={{}}
                  wrapperClass=""
                />
              </div>
            </div>
          </div> */}

        {/* </div> */}
        {showDetails && (
          <div
            style={{
              position: "fixed",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              zIndex: 98,
            }}
          >
            <motion.div
              id={"modalId"}
              ref={modalRef}
              className="candidatedetails"
              style={{

              }}
              animate={{ scale: 1 }}
              initial={{ scale: 0 }}
              transition={{ duration: 1 }}
            >
              <div style={{ display: "flex", justifyContent: "center" }}>
                <h3 style={{ paddingTop: "0px" }}>Candidate Details</h3>
              </div>

              <div
                style={{
                  width: "100%",
                  height: "88%",
                  overflowY: "auto",
                }}
              >
                <table id={"details"}>
                  <tr id={"tr"}>
                    <th id={"th"}>ID:</th>
                    <td id={"td"}>{details.id}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Job Id:</th>
                    <td id={"td"}>{details.job_id}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Name:</th>
                    <td id={"td"}>{details.name}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Mobile:</th>
                    <td id={"td"}>{details.mobile}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Email:</th>
                    <td id={"td"}>{details.email}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Client:</th>
                    <td id={"td"}>{details.client}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Current Company:</th>
                    <td id={"td"}>{details.current_company}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Position:</th>
                    <td id={"td"}>{details.position}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Profile:</th>
                    <td id={"td"}>{details.profile}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Current Job Location:</th>
                    <td id={"td"}>{details.current_job_location}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Preferred Job Location:</th>
                    <td id={"td"}>{details.preferred_job_location}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Qualifications:</th>
                    <td id={"td"}>{details.qualifications}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Total Experience:</th>
                    <td id={"td"}>{details.experience}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Relevant Experience:</th>
                    <td id={"td"}>{details.relevant_experience}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Current ctc:</th>
                    <td id={"td"}>{details.current_ctc}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Expected ctc:</th>
                    <td id={"td"}>{details.expected_ctc}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Notice period:</th>
                    <td id={"td"}>{details.notice_period}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Linkedin:</th>
                    <td id={"td"}>{details.linkedin}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Holding Offer:</th>
                    <td id={"td"}>{details.holding_offer}</td>
                  </tr>
                  <tr id={'tr'}>
                    <th id={'th'}>Recruiter:</th>
                    <td id={'td'}>{details.recruiter}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Management:</th>
                    <td id={"td"}>{details.management}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Status:</th>
                    <td id={"td"}>{details.status}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Remarks:</th>
                    <td id={"td"}>{details.remarks}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Skills:</th>
                    <td id={"td"}>{details.skills}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Period of notice:</th>
                    <td id={"td"}>{details.period_of_notice}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Last working Date:</th>
                    <td id={"td"}>{details.last_working_date}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Date created:</th>
                    <td id={"td"}>{details.date_created}</td>
                  </tr>
                  <tr id={"tr"}>
                    <th id={"th"}>Time created:</th>
                    <td id={"td"}>{details.time_created}</td>
                  </tr>
                </table>
              </div>
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  marginTop: "15px",


                }}
              >
                <button onClick={handleCloseDetails} style={{ padding: "5px 20px", background: "#32406d", color: "#fff", border: "none", borderRadius: "5px" }}>Close</button>
              </div>
            </motion.div>
          </div>
        )}

        <div
          className=" mobiledash useraccco"
          // className={` mobiledash useraccco ${isAnimating ? 'genie-effect' : ''}`}
          style={{ margin: "35px 0xp 5px" }}
        >
          <div className="left-section">
            {/* <button className="useracccot" onClick={openModal} style={{ padding: "5px  10px" }}>New Meeting +</button> */}
            {/* <button className="useracccot" onClick= {()=>navigate('/AccountDeactivation')}> Account Deactivation</button> */}
            < ScheduleMeet
              interviewModal={interviewModal}
              InterviewcloseModal={InterviewcloseModal}
              selectedScheduleData={selectedScheduleData}
            />
          </div>

          <div className="AUheading  center-section">
            <h5 style={{ padding: "0px", marginLeft: "20%" }} className="users">
              All Candidate Details
            </h5>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', zIndex: '2' }} className="right-section">
            <button onClick={handleDownload} style={{ display: 'flex', marginRight: '10px', padding: '3px', justifyContent: 'center', alignItems: 'center', borderRadius: "5px", marginTop: "5px", border: "none", height: '30px', width: "35px" }}>
              <PiMicrosoftExcelLogoFill style={{ marginRight: "0px", fontSize: "25px", color: "#32406d" }} data-tooltip-id={"remove_search"} data-tooltip-content="Data Download" /> {/* Add icon and some styling */}
              <ReactTooltip
                style={{ zIndex: 999, padding: "2px", backgroundColor: "#32406d" }}
                place="top-start"
                id="Resume Upload"
              />
            </button>
            <button
              style={{ cursor: selectedJobs.length === 0 ? 'not-allowed' : 'pointer', height: '30px', width: "30px", color: "#32406d", marginRight: "10px", border: "none", borderRadius: "5px", marginTop: "5px" }}
              disabled={selectedJobs.length === 0}
            >
              <FontAwesomeIcon icon={faShareAlt} onClick={newopenModal} style={{ display: 'flex', alignItems: 'center', height: "22px", width: "22px", marginTop: "0px", marginLeft: "3px" }} disabled={selectedJobs.length === 0} data-tooltip-id={"remove_search"} data-tooltip-content="Share Details" />
              <ReactTooltip
                style={{ zIndex: 999, padding: "2px", backgroundColor: "#32406d" }}
                place="top-start"
                id="Resume Upload"
              />

            </button>
            <div className="remove_filter_icons"
              onClick={() => setSelectedJobs([])}
              style={{
                display: 'flex',
                marginRight: '10px',
                padding: '3px',
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: "5px",
                marginTop: "4px",
                position: 'relative'
              }}>
              {/* Notification Badge */}
              {selectedJobs.length > 0 && (
                <span style={{
                  position: 'absolute',
                  top: '-5px',
                  right: '-5px',
                  backgroundColor: 'red',
                  color: 'white',
                  fontSize: '10px',
                  padding: '2px 6px',
                  borderRadius: '50%',
                  zIndex: 1,
                  fontWeight: 'bold'
                }}>
                  {selectedJobs.length}
                </span>
              )}

              {/* Clear All Icon */}
              <VscClearAll
                style={{
                  cursor: 'pointer',
                  height: '24px',
                  width: "24px",
                  color: "#32406d"
                }}
                data-tooltip-id={"remove_search"}
                data-tooltip-content="Unselect"
              />
              <ReactTooltip
                style={{ zIndex: 999, padding: "2px", backgroundColor: "#32406d" }}
                place="top-start"
                id="remove_search"
              />
            </div>
            <div className="search-container">
              <IoMdSearch style={{ display: 'flex', alignItems: 'center', height: "22px", width: "22px", marginRight: "-25px", marginTop: "5px" }} />
              <input
                placeholder="Search"
                style={{
                  marginTop: "4px",
                  paddingLeft: "26px",
                  height: "30px",
                  width: "200px",
                  backgroundColor: "rgba(255, 255, 255, 0.80)",
                  border: "none",
                  borderRadius: "5px",
                  padding: "0 25px"
                }}
                className="Search"
                value={searchValue}
                onChange={(e) => {
                  // console.log(e.target.value);
                  setSearchValue(e.target.value);
                  // date_created    job_id    name    email   mobile   client    profile    skills    recruiter    status
                }}
              />
            </div>
            {/* <button style={{marginLeft:'20px',backgroundColor: "#32406d",color:'white',border:'none',padding:'4px',borderRadius:'5px'}}
                        onClick={removeAllFilter}
      >clear all filters</button> */}
            {/* <img style={{marginLeft:'20px',height:'24px'}} src={clear_search} alt="svg_img" /> */}
            <div className="remove_filter_icons" onClick={() => {
              setSearchValue('');
            }} style={{ display: 'flex', marginLeft: '10px', padding: '3px', justifyContent: 'center', alignItems: 'center', borderRadius: "5px", marginTop: "4px" }}>
              {/* <img style={{ cursor: 'pointer', height: '24px' }} src={clear_search} alt="svg_img"
                        data-tooltip-id={"remove_search"}
                        data-tooltip-content="Clear search"
                    /> */}
              <MdOutlineYoutubeSearchedFor style={{ cursor: 'pointer', height: '24px', width: "24px", color: "#32406d" }} data-tooltip-id={"remove_search"}
                data-tooltip-content="Clear search" />
              <ReactTooltip
                style={{ zIndex: 999, padding: "2px", backgroundColor: "#32406d" }}
                place="top-start"
                id="remove_search"
              />
            </div>
            <div className="remove_filter_icons" onClick={removeAllFilter} style={{ display: 'flex', marginLeft: '10px', padding: '3px', justifyContent: 'center', alignItems: 'center', borderRadius: "5px", marginTop: "4px" }}>
              <img style={{ cursor: 'pointer', height: '24px' }} src={filter_icon} alt="svg_img"
                data-tooltip-id={"remove_filter"}
                data-tooltip-content="Clear all filters"
              />
              <ReactTooltip
                style={{ zIndex: 999, padding: "4px", backgroundColor: "#32406d" }}
                place="top-start"
                id="remove_filter"
              />
            </div>
          </div>
        </div>
        <div
          className="dashcontainer"
          // className={`dashcontainer ${isAnimating ? 'genie-effect' : ''}`}
          // style={{borderRadius:"0px"}}

          id="scrollable-element"
        >
          <div
            className="table-container"
            style={{
              overflowY: "auto",
              marginTop: "3px",
              overflowX: "auto",
            }}
          >

            <table
              className="max-width-fit-content tble"
              style={{

                width: "100%",
                marginTop: "-5px",
                overflow: "auto",


              }}
              class="table"
              id="candidates-tale"
            >
              <thead>
                <tr  >

                  <th style={{ width: "95px", color: showSearchjobassignment.showSearchdate ? "orange" : "white", fontSize: "13px" }} >
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <span
                        id={"date_label_ref"}
                        onClick={() => {
                          // console.log("Filter icon clicked!");
                          setshowSearchjobassignment((prev) => ({
                            ...Object.fromEntries(
                              Object.keys(prev).map((key) => [
                                key,
                                key === "showSearchdate"
                                  ? !prev.showSearchdate
                                  : false,
                              ]),
                            ),
                          }));
                        }}
                        style={{ cursor: "pointer" }}  > Date</span>
                      <div style={{ display: 'flex', alignItems: 'right' }}>
                        <span
                          onClick={() => handleSort('date_created')}
                          style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                        >
                          {getSortIcon('date_created')}
                        </span>
                        <MdFilterAlt
                          style={{ color: isDateFiltered ? "orange" : "white",
                                  fontSize: '16px',
                            //  marginLeft: '3px'
                             }}
                          id={"date_ref"}
                          className="arrow"
                          onClick={() => {
                            // console.log("Filter icon clicked!");
                            setshowSearchjobassignment((prev) => ({
                              ...Object.fromEntries(
                                Object.keys(prev).map((key) => [
                                  key,
                                  key === "showSearchdate"
                                    ? !prev.showSearchdate
                                    : false,
                                ]),
                              ),
                            }));
                          }}
                        />
                      </div>
                    </div>
                    {showSearchjobassignment.showSearchdate && (
                      <div ref={uniRef} className="Filter-popup">
                        <form
                          id="filter-form"
                          className="Filter-inputs-container"
                        >
                          <ul>
                            <li>
                              <input
                                type="checkbox"
                                style={{
                                  width: "12px",
                                  marginRight: "5px",
                                }}
                                checked={selectAllDate}
                                onChange={handleSelectAllForDate}
                              />
                              <label

                                style={{
                                  marginBottom: "0px",
                                  fontWeight: "400",
                                  fontSize: '13px',
                                  cursor: 'pointer',
                                }}
                                onClick={() => handleSelectAllForDate()}>
                                Select all
                              </label>
                            </li>
                            <li>
                              {uniqueDataDate
    //                             .sort((a, b) => {
    // const inArray2a = dateSelected.includes(a);
    // const inArray2b = dateSelected.includes(b);

    // if (inArray2a && !inArray2b) {
    //   return -1;
    // }
    // else if (!inArray2a && inArray2b) {
    //   return 1;
    // } else {
    //   return new Date(b) - new Date(a);
    // }
    //                             })
                                .map((date_created, index) => (
                                  <div key={index} className="filter-inputs">
                                    <input
                                      type="checkbox"
                                      style={{
                                        width: "12px",
                                      }}
                                      checked={dateSelected.includes(
                                        date_created,
                                      )}
                                      onChange={() =>
                                        handleCheckboxChangeForDate(
                                          date_created,
                                        )
                                      }
                                    />
                                    <label
                                      style={{
                                        marginBottom: "0px",
                                        fontWeight: "400",
                                        cursor: 'pointer',
                                      }}
                                      onClick={() => handleCheckboxChangeForDate(
                                        date_created,
                                      )}
                                    >
                                   {(() => {
    const [year, month, day] = date_created.split("-");
    return `${day}-${month}-${year}`;
  })()}
                                    </label>
                                  </div>
                                ))}
                            </li>
                          </ul>
                        </form>
                        {/* <div className="filter-popup-footer">
                              <button onClick={handleOkClick}>OK</button>
                              <button
                                onClick={() => {
                                  setshowSearchjobassignment((prev) =>
                                    Object.fromEntries(
                                      Object.entries(prev).map(
                                        ([key, value]) => [key, false],
                                      ),
                                    ),
                                  );
                                }}
                              >
                                Cancel
                              </button>
                            </div> */}
                      </div>
                    )}
                  </th>
                  <th
                    style={{
                      width: "80px",
                      fontSize: "13px",
                      display: selectedJobs.length > 0 ? "table-cell" : "none", // Control visibility of the entire column
                    }}
                  >
                    Share
                  </th>

                  <th style={{ width: "80px", color: showSearchjobassignment.showSearchuserId ? "orange" : "white", fontSize: "13px" }}>
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <span
                        style={{ cursor: "pointer" }}
                        id={"job_label_ref"}
                        onClick={() => {
                          setshowSearchjobassignment((prev) => ({
                            ...Object.fromEntries(
                              Object.keys(prev).map((key) => [
                                key,
                                key === "showSearchuserId"
                                  ? !prev.showSearchuserId
                                  : false,
                              ]),
                            ),
                          }));
                        }}
                      >Job Id{" "}</span>
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <span
                          onClick={() => handleSort('job_id')}
                          style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                        >
                          {getSortIcon('job_id')}
                        </span>
                        <MdFilterAlt
                          style={{
                            color: isJobIdFiltered ? "orange" : "white",
                            fontSize: '16px',
                            // marginLeft: '3px'
                          }}
                          id={"job_ref"}
                          className="arrow"
                          onClick={() => {
                            setshowSearchjobassignment((prev) => ({
                              ...Object.fromEntries(
                                Object.keys(prev).map((key) => [
                                  key,
                                  key === "showSearchuserId"
                                    ? !prev.showSearchuserId
                                    : false,
                                ]),
                              ),
                            }));
                          }}
                        />
                      </div>
                    </div>
                    {showSearchjobassignment.showSearchuserId && (
                      <div ref={uniRef} className="Filter-popup">
                        <form
                          id="filter-form-user"
                          className="Filter-inputs-container"
                        >
                          <ul>
                            <li>
                              <input
                                type="checkbox"
                                style={{
                                  width: "12px",
                                  marginRight: "5px",
                                }}
                                checked={selectAllForJobId}
                                onChange={handleSelectAllForUserId}
                              />
                              <label
                                style={{
                                  marginBottom: "0px",
                                  fontWeight: "400",
                                  fontSize: '13px',
                                  cursor: 'pointer',
                                }}
                                onClick={() => handleSelectAllForUserId()}>
                                Select all
                              </label>
                            </li>
                            <li>
                              {uniqueDatajobId
                                .sort((a, b) => {
                                  const inArray2a = jobIdSelected.includes(a.toString());
                                  const inArray2b = jobIdSelected.includes(b.toString());

                                  if (inArray2a && !inArray2b) {
                                    return -1;
                                  }
                                  else if (!inArray2a && inArray2b) {
                                    return 1;
                                  } else {
                                    return a - b;
                                  }

                                })
                                .map((userId, index) => {
                                  return (
                                    <div
                                      key={index}
                                      className="filter-inputs"
                                    >
                                      <input
                                        type="checkbox"
                                        style={{
                                          width: "12px",
                                        }}
                                        checked={jobIdSelected.includes(
                                          userId.toString(),
                                        )}
                                        onChange={() =>
                                          handleCheckboxChangeUser(
                                            userId.toString(),
                                          )
                                        }
                                      />
                                      <label
                                        style={{
                                          marginBottom: "0px",
                                          fontWeight: "400",
                                          cursor: 'pointer',
                                        }}
                                        onClick={() => handleCheckboxChangeUser(
                                          userId.toString(),
                                        )}
                                      >
                                        {userId}
                                      </label>
                                    </div>
                                  );
                                })}
                            </li>
                          </ul>
                        </form>

                        {/* <div className="filter-popup-footer">
                              <button onClick={handleOkClick}>OK</button>
                              <button
                                onClick={() => {
                                  setshowSearchjobassignment((prev) =>
                                    Object.fromEntries(
                                      Object.entries(prev).map(([key]) => [
                                        key,
                                        false,
                                      ]),
                                    ),
                                  );
                                }}
                              >
                                Cancel
                              </button>
                            </div> */}
                      </div>
                    )}
                  </th>
                  <th style={{ width: "150px", color: showSearchjobassignment.showSearchName ? "orange" : "white", fontSize: "13px" }}>
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <span
                        style={{ cursor: "pointer" }}
                        id={"name_label_ref"}
                        onClick={() => {
                          setshowSearchjobassignment((prev) => ({
                            ...Object.fromEntries(
                              Object.keys(prev).map((key) => [
                                key,
                                key === "showSearchName"
                                  ? !prev.showSearchName
                                  : false,
                              ]),
                            ),
                          }));
                        }}
                      >  Name  {" "}</span>
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <span
                          onClick={() => handleSort('name')}
                          style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                        >
                          {getSortIcon('name')}
                        </span>
                        <MdFilterAlt
                          style={{ color: isnameFiltered ? "orange" : "white",
                            fontSize: '16px',
                            // marginLeft: '3px'
                           }}
                          id={"name_ref"}
                          className="arrow"
                          onClick={() => {
                            setshowSearchjobassignment((prev) => ({
                              ...Object.fromEntries(
                                Object.keys(prev).map((key) => [
                                  key,
                                  key === "showSearchName"
                                    ? !prev.showSearchName
                                    : false,
                                ]),
                              ),
                            }));
                          }}
                        />
                      </div>
                    </div>
                    {showSearchjobassignment.showSearchName && (
                      <div ref={uniRef} className="Filter-popup">
                        <form
                          id="filter-form"
                          className="Filter-inputs-container"
                        >
                          <ul>
                            <li>
                              <input
                                type="checkbox"
                                style={{
                                  width: "12px",
                                  marginRight: "5px",
                                }}
                                checked={selectAll}
                                onChange={handleSelectAllForName}
                              />
                              <label

                                style={{
                                  marginBottom: "0px",
                                  fontWeight: "400",
                                  fontSize: '13px',
                                  cursor: 'pointer',
                                }}
                                onClick={() => handleSelectAllForName()}>
                                Select all
                              </label>
                            </li>
                            <li>
                              {uniqueDataNames
                                // .slice()
                                // .filter((name) => name !== undefined)
                                // .sort((a, b) => {
                                //   // const array2 = ["kiwi", "papaya", "orange"]; // Replace this with your actual array
                                //   const trimmedA = a?.trim().toLowerCase();
                                //   const trimmedB = b?.trim().toLowerCase();

                                //   const inArray2A = nameSelected.includes(trimmedA);
                                //   const inArray2B = nameSelected.includes(trimmedB);

                                //   if (inArray2A && !inArray2B) {
                                //     return -1;
                                //   } else if (!inArray2A && inArray2B) {
                                //     return 1;
                                //   } else {
                                //     return trimmedA.localeCompare(trimmedB);
                                //   }
                                // })
                                .map((name, index) => (
                                  <div
                                    key={index}
                                    className="filter-inputs"
                                  >
                                    <input
                                      type="checkbox"
                                      style={{
                                        width: "12px",
                                      }}
                                      checked={nameSelected.includes(
                                        name.toLowerCase(),
                                      )}
                                      onChange={() =>
                                        handleCheckboxChange(
                                          name.toLowerCase(),
                                        )
                                      }
                                    />
                                    <label
                                      style={{
                                        marginBottom: "0px",
                                        fontWeight: "400",
                                        cursor: 'pointer',
                                      }}
                                      onClick={() => handleCheckboxChange(
                                        name.toLowerCase(),
                                      )}
                                    >
                                      {name}
                                    </label>
                                  </div>
                                ))}
                            </li>
                          </ul>
                        </form>

                      </div>
                    )}
                  </th>
                  <th style={{ width: "190px", color: showSearchjobassignment.showSearchEmail ? "orange" : "white", fontSize: "13px" }}>
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <span>Email</span>
                      <span
                        onClick={() => handleSort('email')}
                        style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                      >
                        {getSortIcon('email')}
                      </span>
                    </div>
                    {/* <MdFilterAlt
                      style={{
                        color: isemailFiltered ? "orange" : "white",
                      }}
                      id={"email_ref"}
                      className="arrow"
                      onClick={() => {
                        setshowSearchjobassignment((prev) => ({
                          ...Object.fromEntries(
                            Object.keys(prev).map((key) => [
                              key,
                              key === "showSearchEmail"
                                ? !prev.showSearchEmail
                                : false,
                            ]),
                          ),
                        }));
                      }}
                    /> */}
                    {/* {showSearchjobassignment.showSearchEmail && (
                      <div ref={uniRef} className="Filter-popup">
                        <form
                          id="filter-form-email"
                          className="Filter-inputs-container"
                        >
                          <ul>
                            <li>
                              <input
                                type="checkbox"
                                style={{
                                  width: "12px",
                                  marginRight: "5px",
                                }}
                                checked={selectAllEmail}
                                onChange={handleSelectAllForEmail}
                              />
                              <label
                                onClick={() => handleSelectAllForEmail()}
                                style={{
                                  fontSize: '13px',
                                  fontWeight: '400',
                                  cursor: 'pointer',
                                  marginBottom: "0px",

                                }}>
                                Select all
                              </label>

                            </li>
                            <li>
                              {uniqueDataEmail
                                .slice()
                                .sort((a, b) => {
                                  // const array2 = ["kiwi", "papaya", "orange"]; // Replace this with your actual array
                                  const trimmedA = a?.trim().toLowerCase();
                                  const trimmedB = b?.trim().toLowerCase();

                                  const inArray2A = emailSelected.includes(trimmedA);
                                  const inArray2B = emailSelected.includes(trimmedB);

                                  if (inArray2A && !inArray2B) {
                                    return -1;
                                  } else if (!inArray2A && inArray2B) {
                                    return 1;
                                  } else {
                                    return 0;
                                  }
                                })
                                .map((email, index) => (
                                  <div
                                    key={index}
                                    className="filter-inputs"
                                  >
                                    <input
                                      type="checkbox"
                                      style={{
                                        width: "12px",
                                      }}
                                      checked={emailSelected.includes(
                                        email.toLowerCase(),
                                      )}
                                      onChange={() =>
                                        handleCheckboxChangeEmail(
                                          email.toLowerCase(),
                                        )
                                      }
                                    />
                                    <label
                                      style={{
                                        marginBottom: "0px",
                                        fontWeight: "400",
                                        cursor: 'pointer',
                                      }}
                                      onClick={() => handleCheckboxChangeEmail(
                                        email.toLowerCase(),
                                      )}
                                    >
                                      {email}
                                    </label>
                                  </div>
                                ))}
                            </li>
                          </ul>
                        </form>
                       
                      </div>
                    )} */}
                  </th>
                  <th style={{ width: "95px", color: showSearchjobassignment.showSearchMobile ? "orange" : "white", fontSize: "13px" }}>
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <span>Mobile</span>
                      <span
                        onClick={() => handleSort('mobile')}
                        style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                      >
                        {getSortIcon('mobile')}
                      </span>
                    </div>
                    {/* <MdFilterAlt
                      style={{
                        color: ismobileFiltered ? "orange" : "white",
                      }}
                      id={"mobile_ref"}
                      className="arrow"
                      onClick={() => {
                        setshowSearchjobassignment((prev) => ({
                          ...Object.fromEntries(
                            Object.keys(prev).map((key) => [
                              key,
                              key === "showSearchMobile"
                                ? !prev.showSearchMobile
                                : false,
                            ]),
                          ),
                        }));
                      }}
                    /> */}
                    {/* {showSearchjobassignment.showSearchMobile && (
                      <div ref={uniRef} className="Filter-popup">
                        <form
                          id="filter-form"
                          className="Filter-inputs-container"
                        >
                          <ul>
                            <li>
                              <input
                                type="checkbox"
                                style={{
                                  width: "12px",
                                  marginRight: "5px",
                                }}
                                checked={selectAllMobile}
                                onChange={handleSelectAllForMobile}
                              />
                              <label
                                onClick={() => handleSelectAllForMobile()}
                                style={{
                                  cursor: 'pointer',
                                  marginBottom: '0px',
                                  fontWeight: '400',
                                  fontSize: '13px',
                                }}>
                                Select all
                              </label>

                            </li>
                            <li>
                              {uniqueDataMobile
                                .slice()
                                .sort((a, b) => {
                                  // const array2 = ["kiwi", "papaya", "orange"]; // Replace this with your actual array
                                  const trimmedA = a?.trim();
                                  const trimmedB = b?.trim();

                                  const inArray2A = mobileSelected.includes(trimmedA);
                                  const inArray2B = mobileSelected.includes(trimmedB);

                                  if (inArray2A && !inArray2B) {
                                    return -1;
                                  } else if (!inArray2A && inArray2B) {
                                    return 1;
                                  } else {
                                    return 0;
                                  }
                                })
                                .map((mobile, index) => (
                                  <div key={index} className="filter-inputs">
                                    <input
                                      type="checkbox"
                                      style={{
                                        width: "12px",
                                      }}
                                      checked={mobileSelected.includes(
                                        mobile,
                                      )}
                                      onChange={() =>
                                        handleCheckBoxChangeForMobile(mobile)
                                      }
                                    />
                                    <label
                                      style={{
                                        marginBottom: "0px",
                                        fontWeight: "400",
                                        cursor: 'pointer',
                                      }}
                                      onClick={() => handleCheckBoxChangeForMobile(mobile)}
                                    >
                                      {mobile}
                                    </label>
                                  </div>
                                ))}
                            </li>
                          </ul>
                        </form>
                     
                      </div>
                    )} */}
                  </th>

                  <th style={{ width: "120px", color: showSearchjobassignment.showSearchClient ? "orange" : "white", fontSize: "13px" }}>
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <span
                        style={{ cursor: "pointer" }}
                        id={"client_label_ref"}
                        onClick={() => {
                          setshowSearchjobassignment((prev) => ({
                            ...Object.fromEntries(
                              Object.keys(prev).map((key) => [
                                key,
                                key === "showSearchClient"
                                  ? !prev.showSearchClient
                                  : false,
                              ]),
                            ),
                          }));
                        }}
                      >Client{" "}</span>
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <span
                          onClick={() => handleSort('client')}
                          style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                        >
                          {getSortIcon('client')}
                        </span>
                        <MdFilterAlt
                          style={{
                            color: isclientFiltered ? "orange" : "white",
                            fontSize: '16px',
                            // marginLeft: '3px'
                          }}
                          id={"client_ref"}
                          className="arrow"
                          onClick={() => {
                            setshowSearchjobassignment((prev) => ({
                              ...Object.fromEntries(
                                Object.keys(prev).map((key) => [
                                  key,
                                  key === "showSearchClient"
                                    ? !prev.showSearchClient
                                    : false,
                                ]),
                              ),
                            }));
                          }}
                        />
                      </div>
                    </div>
                    {showSearchjobassignment.showSearchClient && (
                      <div ref={uniRef} className="Filter-popup">
                        <form
                          id="filter-form-client"
                          className="Filter-inputs-container"
                        >
                          <ul>
                            <li>
                              <input
                                type="checkbox"
                                style={{
                                  width: "12px",
                                  marginRight: "5px",
                                }}
                                checked={selectAllClient}
                                onChange={handleSelectAllForClient}
                              />
                              <label
                                style={{
                                  marginBottom: "0px",
                                  fontWeight: "400",
                                  cursor: 'pointer',
                                  fontSize: '13px',
                                }}
                                onClick={() => handleSelectAllForClient()} >
                                Select all
                              </label>

                            </li>
                            <li>
                              {uniqueDataClient
                                // .slice()
                                // .sort((a, b) => {
                                //   // const array2 = ["kiwi", "papaya", "orange"]; // Replace this with your actual array
                                //   const trimmedA = a?.trim().toLowerCase();
                                //   const trimmedB = b?.trim().toLowerCase();

                                //   const inArray2A = clientSelected.includes(trimmedA);
                                //   const inArray2B = clientSelected.includes(trimmedB);

                                //   if (inArray2A && !inArray2B) {
                                //     return -1;
                                //   } else if (!inArray2A && inArray2B) {
                                //     return 1;
                                //   } else {
                                //     return trimmedA.localeCompare(trimmedB);
                                //   }
                                // })
                                .map((client, index) => (
                                  <div
                                    key={index}
                                    className="filter-inputs"
                                  >
                                    <input
                                      type="checkbox"
                                      style={{
                                        width: "12px",
                                      }}
                                      checked={clientSelected.includes(
                                        client.toLowerCase(),
                                      )}
                                      onChange={() =>
                                        handleCheckboxChangeClient(
                                          client.toLowerCase(),
                                        )
                                      }
                                    />
                                    <label
                                      style={{
                                        marginBottom: "0px",
                                        fontWeight: "400",
                                        cursor: 'pointer',
                                      }}
                                      onClick={() => handleCheckboxChangeClient(
                                        client.toLowerCase(),
                                      )}
                                    >
                                      {client}
                                    </label>
                                  </div>
                                ))}
                            </li>
                          </ul>
                        </form>
                        {/* <div className="filter-popup-footer">
                              <button onClick={handleOkClick}>OK</button>
                              <button
                                onClick={() => {
                                  setshowSearchjobassignment((prev) =>
                                    Object.fromEntries(
                                      Object.entries(prev).map(
                                        ([key, value]) => [key, false],
                                      ),
                                    ),
                                  );
                                }}
                              >
                                Cancel
                              </button>
                            </div> */}
                      </div>
                    )}
                  </th>
                  <th style={{ width: "90px", color: showSearchjobassignment.showSearchProfile ? "orange" : "white", fontSize: "13px" }}>
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <span
                        style={{ cursor: "pointer" }}
                        id={"profile_label_ref"}
                        onClick={() => {
                          setshowSearchjobassignment((prev) => ({
                            ...Object.fromEntries(
                              Object.keys(prev).map((key) => [
                                key,
                                key === "showSearchProfile"
                                  ? !prev.showSearchProfile
                                  : false,
                              ]),
                            ),
                          }));
                        }}
                      >Profile{" "}</span>
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <span
                          onClick={() => handleSort('profile')}
                          style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                        >
                          {getSortIcon('profile')}
                        </span>
                        <MdFilterAlt
                          style={{
                            color: isprofileFiltered ? "orange" : "white",
                            fontSize: '16px',
                            // marginLeft: '3px'
                          }}
                          id={"profile_ref"}
                          name=""
                          className="arrow"
                          onClick={() => {
                            setshowSearchjobassignment((prev) => ({
                              ...Object.fromEntries(
                                Object.keys(prev).map((key) => [
                                  key,
                                  key === "showSearchProfile"
                                    ? !prev.showSearchProfile
                                    : false,
                                ]),
                              ),
                            }));
                          }}
                        />
                      </div>
                    </div>
                    {showSearchjobassignment.showSearchProfile && (
                      <div ref={uniRef} className="Filter-popup">
                        <form
                          id="filter-form-profile"
                          className="Filter-inputs-container"
                        >
                          <ul>
                            <li>
                              <input
                                type="checkbox"
                                style={{
                                  width: "12px",
                                  marginRight: "5px",
                                }}
                                checked={selectAllProfile}
                                onChange={handleSelectAllForProfile}
                              />
                              <label
                                style={{
                                  marginBottom: "0px",
                                  fontWeight: "400",
                                  cursor: 'pointer',
                                  fontSize: '13px',
                                }}
                                onClick={() => handleSelectAllForProfile()} >
                                Select all
                              </label>
                            </li>
                            <li>
                              {uniqueDataProfile
                                // .slice()
                                // .sort((a, b) => {
                                //   // const array2 = ["kiwi", "papaya", "orange"]; // Replace this with your actual array
                                //   const trimmedA = a?.trim().toLowerCase();
                                //   const trimmedB = b?.trim().toLowerCase();

                                //   const inArray2A = profileSelected.includes(trimmedA);
                                //   const inArray2B = profileSelected.includes(trimmedB);

                                //   if (inArray2A && !inArray2B) {
                                //     return -1;
                                //   } else if (!inArray2A && inArray2B) {
                                //     return 1;
                                //   } else {
                                //     return 0;
                                //   }
                                // })
                                .map((profile, index) => (
                                  <div
                                    key={index}
                                    className="filter-inputs"
                                  >
                                    <input
                                      type="checkbox"
                                      style={{
                                        width: "12px",
                                      }}
                                      checked={profileSelected.includes(
                                        profile?.toLowerCase(),
                                      )}
                                      onChange={() =>
                                        handleCheckboxChangeProfile(
                                          profile.toLowerCase(),
                                        )
                                      }
                                    />
                                    <label
                                      style={{
                                        marginBottom: "0px",
                                        fontWeight: "400",
                                        cursor: 'pointer',
                                      }}
                                      onClick={() => handleCheckboxChangeProfile(
                                        profile.toLowerCase(),
                                      )}
                                    >
                                      {profile}
                                    </label>
                                  </div>
                                ))}
                            </li>
                          </ul>
                        </form>
                        {/* <div className="filter-popup-footer">
                              <button onClick={handleOkClick}>OK</button>
                              <button
                                onClick={() => {
                                  setshowSearchjobassignment((prev) =>
                                    Object.fromEntries(
                                      Object.entries(prev).map(
                                        ([key, value]) => [key, false],
                                      ),
                                    ),
                                  );
                                }}
                              >
                                Cancel
                              </button>
                            </div> */}
                      </div>
                    )}
                  </th>
                  <th style={{ width: "100px", color: showSearchjobassignment.showSearchSkill ? "orange" : "white", fontSize: "13px" }}>
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <span
                        style={{ cursor: "pointer" }}
                        id={"skills_label_ref"}
                        onClick={() => {
                          setshowSearchjobassignment((prev) => ({
                            ...Object.fromEntries(
                              Object.keys(prev).map((key) => [
                                key,
                                key === "showSearchSkill"
                                  ? !prev.showSearchSkill
                                  : false,
                              ]),
                            ),
                          }));
                        }}
                      >Skills{" "}</span>
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <span
                          onClick={() => handleSort('skills')}
                          style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                        >
                          {getSortIcon('skills')}
                        </span>
                        <MdFilterAlt
                          style={{
                            color: isskillFiltered ? "orange" : "white",
                            fontSize: '16px',
                            // marginLeft: '3px'
                          }}
                          id={"skills_ref"}
                          className="arrow"
                          onClick={() => {
                            setshowSearchjobassignment((prev) => ({
                              ...Object.fromEntries(
                                Object.keys(prev).map((key) => [
                                  key,
                                  key === "showSearchSkill"
                                    ? !prev.showSearchSkill
                                    : false,
                                ]),
                              ),
                            }));
                          }}
                        />
                      </div>
                    </div>
                    {showSearchjobassignment.showSearchSkill && (
                      <div
                        ref={uniRef}
                        className="Filter-popup"
                        style={{ width: "300px" }}
                      >
                        <form
                          id="filter-form"
                          className="Filter-inputs-container"
                        >
                          <ul>
                            <li>
                              <input
                                type="checkbox"
                                style={{
                                  width: "12px",
                                  marginRight: "5px",
                                }}
                                checked={selectAllSkill}
                                onChange={handleSelectAllForSkill}
                              />
                              <label
                                style={{
                                  marginBottom: "0px",
                                  fontWeight: "400",
                                  cursor: 'pointer',
                                  fontSize: '13px',
                                }}
                                onClick={() => handleSelectAllForSkill()} >
                                Select all
                              </label>
                            </li>
                            <li>
                              {uniqueDataSkill
                                // .filter((skill) => skill !== null && skill !== "" && skill !== undefined)
                                // .slice()
                                // .sort((a, b) => {
                                //   // const array2 = ["kiwi", "papaya", "orange"]; // Replace this with your actual array
                                //   const trimmedA = a?.trim().toLowerCase();
                                //   const trimmedB = b?.trim().toLowerCase();

                                //   const inArray2A = skillSelected.includes(trimmedA);
                                //   const inArray2B = skillSelected.includes(trimmedB);

                                //   if (inArray2A && !inArray2B) {
                                //     return -1;
                                //   } else if (!inArray2A && inArray2B) {
                                //     return 1;
                                //   } else {
                                //     return 0;
                                //   }
                                // })
                                .map((skills, index) => (
                                  <div
                                    key={index}
                                    className="filter-inputs"
                                  >
                                    <input
                                      type="checkbox"
                                      style={{
                                        width: "12px",
                                      }}
                                      checked={skillSelected.includes(
                                        skills.toLowerCase(),
                                      )}
                                      onChange={() =>
                                        handleCheckboxChangeSkill(
                                          skills.toLowerCase(),
                                        )
                                      }
                                    />
                                    <label
                                      style={{
                                        marginBottom: "0px",
                                        fontWeight: "400",
                                        cursor: 'pointer',
                                      }}
                                      onClick={() => handleCheckboxChangeSkill(
                                        skills.toLowerCase(),
                                      )}
                                    >
                                      {skills}
                                    </label>
                                  </div>
                                ))}
                            </li>
                          </ul>
                        </form>
                        {/* <div className="filter-popup-footer">
                              <button onClick={handleOkClick}>OK</button>
                              <button
                                onClick={() => {
                                  setshowSearchjobassignment((prev) =>
                                    Object.fromEntries(
                                      Object.entries(prev).map(
                                        ([key, value]) => [key, false],
                                      ),
                                    ),
                                  );
                                }}
                              >
                                Cancel
                              </button>
                            </div> */}
                      </div>
                    )}
                  </th>
                  {USERTYPE === "managment" ? (
                    <th style={{ width: "120px", color: showSearchjobassignment.showSearchRecruiter ? "orange" : "white", fontSize: "13px" }}>
                      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <span
                          style={{ cursor: "pointer" }}
                          id={"recruiter_label_ref"}
                          onClick={() => {
                            setshowSearchjobassignment((prev) => ({
                              ...Object.fromEntries(
                                Object.keys(prev).map((key) => [
                                  key,
                                  key === "showSearchRecruiter"
                                    ? !prev.showSearchRecruiter
                                    : false,
                                ]),
                              ),
                            }));
                          }}
                        > Recruiter{" "}</span>
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          <span
                            onClick={() => handleSort('recruiter')}
                            style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                          >
                            {getSortIcon('recruiter')}
                          </span>
                          <MdFilterAlt
                            style={{
                              color: isrecruiterFiltered ? "orange" : "white",
                              fontSize: '16px',
                              // marginLeft: '3px'
                            }}
                            id={"recruiter_ref"}
                            className="arrow"
                            onClick={() => {
                              setshowSearchjobassignment((prev) => ({
                                ...Object.fromEntries(
                                  Object.keys(prev).map((key) => [
                                    key,
                                    key === "showSearchRecruiter"
                                      ? !prev.showSearchRecruiter
                                      : false,
                                  ]),
                                ),
                              }));
                            }}
                          />
                        </div>
                      </div>
                      {showSearchjobassignment.showSearchRecruiter && (
                        <div ref={uniRef} className="Filter-popup">
                          <form
                            id="filter-form"
                            className="Filter-inputs-container"
                          >
                            <ul>
                              <li>
                                <input
                                  type="checkbox"
                                  style={{
                                    width: "12px",
                                    marginRight: "5px",
                                  }}
                                  checked={selectAllRecruiter}
                                  onChange={handleSelectAllForRecruiter}
                                />
                                <label
                                  style={{
                                    marginBottom: "0px",
                                    fontWeight: "400",
                                    cursor: 'pointer',
                                    fontSize: '13px',
                                  }}
                                  onClick={() => handleSelectAllForRecruiter()} >
                                  Select all
                                </label>
                              </li>
                              <li>
                                {uniqueDataRecruiter
                                  // .filter((recruiter) => recruiter != null && recruiter !== "")

                                  // .slice()
                                  // .sort((a, b) => {
                                  //   // const array2 = ["kiwi", "papaya", "orange"]; // Replace this with your actual array
                                  //   const trimmedA = a?.trim().toLowerCase();
                                  //   const trimmedB = b?.trim().toLowerCase();

                                  //   const inArray2A = recruiterSelected.includes(trimmedA);
                                  //   const inArray2B = recruiterSelected.includes(trimmedB);

                                  //   if (inArray2A && !inArray2B) {
                                  //     return -1;
                                  //   } else if (!inArray2A && inArray2B) {
                                  //     return 1;
                                  //   } else {
                                  //     return trimmedA.localeCompare(trimmedB);
                                  //   }
                                  // })
                                  .map((recruiter, index) => (
                                    <div
                                      key={index}
                                      className="filter-inputs"
                                    >
                                      <input
                                        type="checkbox"
                                        style={{
                                          width: "12px",
                                        }}
                                        checked={recruiterSelected.includes(
                                          recruiter.toLowerCase(),
                                        )}
                                        onChange={() =>
                                          handleCheckboxChangeRecruiter(
                                            recruiter.toLowerCase(),
                                          )
                                        }
                                      />
                                      <label
                                        style={{
                                          marginBottom: "0px",
                                          fontWeight: "400",
                                          cursor: 'pointer',
                                        }}
                                        onClick={() => handleCheckboxChangeRecruiter(
                                          recruiter.toLowerCase(),
                                        )}
                                      >
                                        {recruiter === " " ? "Manager" : recruiter}
                                        {console.log("fvsvsfvsfvsfvsfs;", recruiter)}
                                      </label>
                                    </div>
                                  ))}
                              </li>
                            </ul>
                          </form>
                          {/* <div className="filter-popup-footer">
                                <button onClick={handleOkClick}>OK</button>
                                <button
                                  onClick={() => {
                                    setshowSearchjobassignment((prev) =>
                                      Object.fromEntries(
                                        Object.entries(prev).map(
                                          ([key, value]) => [key, false],
                                        ),
                                      ),
                                    );
                                  }}
                                >
                                  Cancel
                                </button>
                              </div> */}
                        </div>
                      )}
                    </th>
                  ) : null}

                  <th style={{ width: "130px", color: showSearchjobassignment.showSearchStatus ? "orange" : "white", fontSize: "13px" }}>
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <span
                        style={{ cursor: "pointer" }}
                        id={"status_label_ref"}
                        onClick={() => {
                          setshowSearchjobassignment((prev) => ({
                            ...Object.fromEntries(
                              Object.keys(prev).map((key) => [
                                key,
                                key === "showSearchStatus"
                                  ? !prev.showSearchStatus
                                  : false,
                              ]),
                            ),
                          }));
                        }}
                      >Status{" "}</span>
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <span
                          onClick={() => handleSort('status')}
                          style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                        >
                          {getSortIcon('status')}
                        </span>
                        <MdFilterAlt
                          style={{
                            color: isstatusFiltered || isColorFiltered ? "orange" : "white",
                            fontSize: '16px',
                            // marginLeft: '3px'
                          }}
                          id={"status_ref"}
                          className="arrow"
                          onClick={() => {
                            setshowSearchjobassignment((prev) => ({
                              ...Object.fromEntries(
                                Object.keys(prev).map((key) => [
                                  key,
                                  key === "showSearchStatus"
                                    ? !prev.showSearchStatus
                                    : false,
                                ]),
                              ),
                            }));
                          }}
                        />
                      </div>
                    </div>
                    {showSearchjobassignment.showSearchStatus && (
                      <div ref={uniRef} className="Filter-popup">
                        <form
                          id="filter-form-client"
                          className="Filter-inputs-container"
                        >
                          <div style={{ backgroundColor: "#cad1ff", height: "65px" }}>
                            <p>Filter by color</p>
                            <div style={{ display: "flex", justifyContent: "space-around", marginBottom: "10px" }}>

                              <input
                                type="radio"
                                id="green"
                                name="green"
                                value="green"
                                checked={selectedColors[0]}
                                onClick={() => handleSelectedColors(0)}

                                style={{
                                  appearance: 'none',
                                  width: '20px',
                                  height: '20px',
                                  borderRadius: '50%',
                                  border: '2px solid green',
                                  outline: 'none',
                                  marginRight: '5px',
                                  backgroundColor: selectedColors[0] ? 'green' : 'white',
                                }}
                              />
                              <input
                                type="radio"
                                id="red"
                                name="red"
                                value="red"
                                checked={selectedColors[1]}
                                onClick={() => handleSelectedColors(1)}
                                style={{
                                  appearance: 'none',
                                  width: '20px',
                                  height: '20px',
                                  borderRadius: '50%',
                                  border: '2px solid orange', // Red border for default and selected state
                                  outline: 'none',
                                  marginRight: '5px',
                                  backgroundColor: selectedColors[1] ? 'orange' : 'white',
                                }}
                              />
                              <input
                                type="radio"
                                id="red"
                                name="red"
                                value="red"
                                checked={selectedColors[2]}
                                onClick={() => handleSelectedColors(2)}
                                style={{
                                  appearance: 'none',
                                  width: '20px',
                                  height: '20px',
                                  borderRadius: '50%',
                                  border: '2px solid red',
                                  outline: 'none',
                                  marginRight: '5px',
                                  backgroundColor: selectedColors[2] ? 'red' : 'white',
                                }}
                              />

                            </div>
                          </div>
                          {/* color for status */}
                          <ul>
                            <li>
                              <input
                                type="checkbox"
                                style={{
                                  width: "12px",
                                  marginRight: "5px",
                                }}
                                checked={selectAllStatus}
                                onChange={handleSelectAllForStatus}
                              />
                              <label
                                style={{
                                  marginBottom: "0px",
                                  fontWeight: "400",
                                  cursor: 'pointer',
                                  fontSize: '13px',
                                }}
                                onClick={() => handleSelectAllForStatus()} >
                                Select all
                              </label>
                            </li>
                            <li>
                              {uniqueDataStatus
                                // .filter(
                                //   (status) =>
                                //     status != null && status !== "",
                                // )
                                // .slice()
                                // .sort((a, b) => {
                                //   // const array2 = ["kiwi", "papaya", "orange"]; // Replace this with your actual array
                                //   const trimmedA = a?.trim().toLowerCase();
                                //   const trimmedB = b?.trim().toLowerCase();

                                //   const inArray2A = statusSelected.includes(trimmedA);
                                //   const inArray2B = statusSelected.includes(trimmedB);

                                //   if (inArray2A && !inArray2B) {
                                //     return -1;
                                //   } else if (!inArray2A && inArray2B) {
                                //     return 1;
                                //   } else {
                                //     return trimmedA.localeCompare(trimmedB);
                                //   }
                                // })
                                .map((status, index) => (
                                  <div
                                    key={index}
                                    className="filter-inputs"
                                  >
                                    <input
                                      type="checkbox"
                                      style={{
                                        width: "12px",
                                      }}
                                      checked={statusSelected.includes(
                                        status.toLowerCase(),
                                      )}
                                      onChange={() =>
                                        handleCheckboxChangeStatus(
                                          status.toLowerCase(),
                                        )
                                      }
                                    />
                                    <label
                                      style={{
                                        marginBottom: "0px",
                                        fontWeight: "400",
                                        cursor: 'pointer',
                                      }}
                                      onClick={() => handleCheckboxChangeStatus(
                                        status.toLowerCase(),
                                      )}
                                    >
                                      {status}
                                    </label>
                                  </div>
                                ))}
                            </li>
                          </ul>
                        </form>
                        {/* <div className="filter-popup-footer">
                              <button onClick={handleOkClick}>OK</button>
                              <button
                                onClick={() => {
                                  setshowSearchjobassignment((prev) =>
                                    Object.fromEntries(
                                      Object.entries(prev).map(
                                        ([key, value]) => [key, false],
                                      ),
                                    ),
                                  );
                                }}
                              >
                                Cancel
                              </button>
                            </div> */}
                      </div>
                    )}
                  </th>
                  {/* {USERTYPE === "recruiter" ? ( */}
                    <th style={{ width: "180px", fontSize: "13px" }}>
                      Comment
                    </th>
                  {/* ) : null} */}

            
                  <th style={{ width: "110px", fontSize: "13px" }}>Schedule Meet </th>
                  <th style={{ width: "45px", fontSize: "13px" }}>Chat</th>
                  <th style={{ width: "60px", fontSize: "13px" }}>Resume </th>
                  <th style={{ width: "55px", fontSize: "13px" }}>Details </th>
                  <th style={{ width: "60px", fontSize: "13px" }}>Update </th>
                  <th style={{ width: "40px", fontSize: "13px" }}>Edit </th>
                    <th style={{ width: "120px", fontSize: "13px" }}>Additional Files </th>
                  {USERTYPE === "managment" ? (
                    <th style={{ width: "50px", fontSize: "13px" }}>Delete </th>
                  ) : null}
                </tr>
              </thead>
              {loading ? (
                <div className="loader-container">
                  <Hourglass
                    // visible={true}
                    height="500"
                    width="60"
                    ariaLabel="hourglass-loading"
                    wrapperStyle={{}}
                    wrapperClass=""
                    colors={["#306cce", "#72a1ed"]}
                    style={{ zIndex: "1000" }}
                  />
                </div>
              ) : (
                <tbody className="scrollable-body">
                  {items?.length === 0 ? (
                    <tr>
                      <td colSpan={USERTYPE === "managment" ? 15 : 13} style={{ textAlign: 'center' }}>
                        No data available in table
                      </td>
                    </tr>
                  ) : (
                    items?.map((item, idx) => {
                      const isDisabled =
                        selectedJobs.length > 0 && currentClient && currentClient === item.client;

                      const showCheckbox = selectedJobs.length > 0;

                      return (
                        <tr key={item.id} style={{ cursor: 'pointer' }}
                        //  class="candidate-row fade-in-element slide-in-element row-animation-delay"
                        >
                          <td
                            onClick={() => toggleRowSelection(item)}
                            style={{
                              textAlign: "center",
                              padding: "2px",
                              borderBottom: "1px solid #ddd",
                              whiteSpace: "normal",
                              wordWrap: "break-word",
                            }}
                          >
                          {(() => {
    const [year, month, day] = item.date_created.split("-");
    return `${day}-${month}-${year}`;
  })()}
                          </td>

                          {/* Conditional rendering for the checkbox column */}
                          {showCheckbox && (
                            <td>
                              <input
                                type="checkbox"
                                style={{ width: "12px" }}
                                checked={selectedJobs.includes(item.id)}
                                onChange={() => handleCheckboxClick(item)}
                              // disabled={isDisabled}
                              />
                            </td>
                          )}
                          <td
                            onClick={() => toggleRowSelection(item)}
                            style={{
                              textAlign: "left",
                              padding: "5px",
                              borderBottom: "1px solid #ddd",
                              whiteSpace: "normal",
                              wordWrap: "break-word",
                            }}
                          >
                            {item.job_id}
                          </td>
                          <td
                            onClick={() => toggleRowSelection(item)}
                            style={{
                              textAlign: "left",
                              padding: "5px",
                              borderBottom: "1px solid #ddd",
                              whiteSpace: "normal",
                              wordWrap: "break-word",
                              // width:"200px",
                              // backgroundColor:"red"
                            }}
                          >
                            {item.name}
                          </td>
                          <td
                            name="email_td"
                            onClick={() => toggleRowSelection(item)}
                            id={list[idx]?.id + "1"}
                            style={{
                              textAlign: "left",
                              padding: "5px",
                              borderBottom: "1px solid #ddd",
                            }}
                          >
                            {item.email}
                            {list && list[idx]?.email && (
                              <div
                                style={{
                                  position: "absolute",
                                  maxWidth: "350px",
                                  backgroundColor: "rgba(255, 255, 255, 0.8)",
                                  border: "1px solid #666",
                                  borderRadius: "10px",
                                  padding: "10px",
                                  boxShadow: "0px 0px 10px rgba(0,0,0,0.2)",
                                  zIndex: 999,
                                  whiteSpace: "normal",
                                  wordWrap: "break-word",
                                }}
                              >
                                {item.email}
                              </div>
                            )}
                          </td>
                          <td
                            onClick={() => toggleRowSelection(item)}
                            style={{
                              textAlign: "center",
                              padding: "2px",
                              borderBottom: "1px solid #ddd",
                              whiteSpace: "normal",
                              wordWrap: "break-word",
                            }}
                          >
                            <span
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                window.location.href = `tel:${item.mobile.replace(/\D/g, '')}`;
                              }}
                              style={{ color: 'inherit', cursor: 'pointer' }}
                            >
                              {item.mobile}
                            </span>
                          </td>
                          <td
                            onClick={() => toggleRowSelection(item)}
                            style={{
                              textAlign: "left",
                              padding: "5px",
                              borderBottom: "1px solid #ddd",
                              whiteSpace: "normal",
                              wordWrap: "break-word",
                            }}
                          >
                            {item.client}
                          </td>
                          <td
                            onClick={() => toggleRowSelection(item)}
                            id={list[idx]?.id + "2"}
                            style={{
                              textAlign: "left",
                              padding: "5px",
                              borderBottom: "1px solid #ddd",
                            }}
                          >
                            {item.profile}
                            {list && list[idx]?.profile && (
                              <div
                                style={{
                                  position: "absolute",
                                  maxWidth: "350px",
                                  backgroundColor: "rgba(255, 255, 255, 0.8)",
                                  border: "1px solid #666",
                                  borderRadius: "10px",
                                  padding: "10px",
                                  boxShadow: "0px 0px 10px rgba(0,0,0,0.2)",
                                  zIndex: 9999,
                                  whiteSpace: "normal",
                                  wordWrap: "break-word",
                                }}
                              >
                                {item.profile}
                              </div>
                            )}
                          </td>
                          <td
                            onClick={() => toggleRowSelection(item)}
                            name="skills_td"
                            id={list[idx]?.id + "3"}
                            style={{
                              textAlign: "left",
                              padding: "5px",
                              borderBottom: "1px solid #ddd",
                            }}
                          >
                            {item.skills}
                            {list && list[idx]?.skills && (
                              <div
                                style={{
                                  position: "absolute",
                                  maxWidth: "350px",
                                  height: "auto",
                                  backgroundColor: "rgba(255, 255, 255, 0.8)",
                                  border: "1px solid #666",
                                  borderRadius: "10px",
                                  padding: "10px",
                                  boxShadow: "0px 0px 10px rgba(0,0,0,0.2)",
                                  zIndex: 9999,
                                  whiteSpace: "normal",
                                  wordWrap: "break-word",
                                }}
                              >
                                {item.skills}
                              </div>
                            )}
                          </td>

                          {USERTYPE === "managment" ? (
                            <td
                              onClick={() => toggleRowSelection(item)}
                              style={{
                                textAlign: "left",
                                //backgroundColor: selectedJobs.includes(item.id) && item.client === currentClient ? '#d3f9d8' : '',

                                padding: "5px",
                                borderBottom: "1px solid #ddd",
                                whiteSpace: "normal",
                                wordWrap: "break-word",
                              }}
                            >
                              {item.recruiter ? item.recruiter : item.management}
                              <br />
                              {item.recruiter ? "" : "(Manager)"}
                            </td>
                          ) : null}
                          <td
                            onClick={() => toggleRowSelection(item)}
                            style={{
                              textAlign: "left",
                              padding: "5px",
                              borderBottom: "1px solid #ddd",
                              whiteSpace: "normal",
                              wordWrap: "break-word",
                              color: getStatusColor(item.status),
                            }}
                          >
                            {item.status}
                          </td>
                          {/* {USERTYPE === "recruiter" ? ( */}
                            <td
                              style={{
                                textAlign: "left",
                                borderBottom: "1px solid #ddd",
                                fontSize: "22px",
                                  padding: "5px"
                              }}
                            >
                              <span
                                style={{
                                  position: "relative",
                                  cursor: "pointer",
                                  fontSize: "14px",
                                  color: "red",
                                  textAlign:"Left"
                                }}
                                onClick={() => {
                                  setVisibleCommentId((prev) => (prev === item.id ? null : item.id));
                                }}
                              >
                                {/* Show latest comment */}
                                {item.comments && typeof item.comments === "object" && (() => {
                                  const allComments = [];

                                  Object.entries(item.comments).forEach(([user, value]) => {
                                    if (Array.isArray(value)) {
                                      value.forEach((entry) => {
                                        if (entry.comment?.trim()) {
                                          allComments.push({ ...entry, user });
                                        }
                                      });
                                    } else if (value?.comment?.trim()) {
                                      allComments.push({ ...value, user });
                                    }
                                  });

                                  if (allComments.length === 0) return null;

                                  // Sort by latest timestamp
                                  const sorted = allComments.sort(
                                    (a, b) => new Date(b.timestamp) - new Date(a.timestamp)
                                  );

                                  return (
                                    <span style={{ fontSize: "13px", color: "#555" }}>
                                      {sorted[0].comment}
                                    </span>
                                  );
                                })()}
                              </span>

                              {/* Popup: all comments */}
                              {visibleCommentId === item.id && (
                                <div
                                  style={{
                                    position: "absolute",
                                    maxWidth: "400px",
                                    maxHeight:"250px",
                                    overflowY: "auto",
                                    backgroundColor: "rgba(255, 255, 255, 0.96)",
                                    border: "1px solid #666",
                                    borderRadius: "10px",
                                    padding: "10px",
                                    boxShadow: "0px 0px 10px rgba(0,0,0,0.2)",
                                    zIndex: 9999,
                                    whiteSpace: "normal",
                                    wordWrap: "break-word",
                                  }}
                                >
                                  <strong
                                    style={{
                                      fontSize: "17px",
                                      display: "block",
                                      textAlign: "center",
                                      marginBottom: "2px",
                                    }}
                                  >
                                    Comments:
                                  </strong>

                                  {/* Map normalized comments */}
                                  {(() => {
                                    const allComments = [];

                                    Object.entries(item.comments || {}).forEach(([user, value]) => {
                                      if (Array.isArray(value)) {
                                        value.forEach((entry) => {
                                          if (entry.comment?.trim()) {
                                            allComments.push({ ...entry, user });
                                          }
                                        });
                                      } else if (value?.comment?.trim()) {
                                        allComments.push({ ...value, user });
                                      }
                                    });

                                    if (allComments.length === 0) {
                                      return <p style={{ fontSize: "13px", color: "#777" }}>No comments available.</p>;
                                    }

                                    // Sort by latest
                                   const sorted = allComments.sort(
  (a, b) => new Date(a.timestamp) - new Date(b.timestamp)
);

                                    return sorted.map((data, i) => (
                                      <div
                                        key={i}
                                        style={{
                                          marginBottom: "10px",
                                          padding: "5px 10px",
                                          background: "#f5f5f5",
                                          borderRadius: "6px",
                                        }}
                                      >
                                        
                                        <p
                                          style={{
                                            fontSize: "13px",
                                            color: "#333",
                                            margin: "5px 0",
                                            textAlign: "justify",
                                          }}
                                        >
                                          {data.comment}
                                        </p>
                                        <span
                                          style={{
                                            fontSize: "11px",
                                            color: "#000",
                                            display: "block",
                                            textAlign: "right",
                                          }}
                                        >
                                          {data.timestamp} <br /> - {data.user}
                                        </span>
                                      </div>
                                    ));
                                  })()}
                                </div>
                              )}
                            </td>
{/* 

                          // ) : null} */}
                          <td onClick={() => openModal(item)} style={{ fontSize: "20px" }}>
                            <RiCalendarScheduleFill style={{ color: "blue" }}
                            />
                          </td>
                          <td>
                            <a
                              href={`https://wa.me/91${item.mobile.replace(/\D/g, '')}`}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              <FaWhatsapp style={{ fontSize: '22px', color: '#25D366' }} />
                            </a>
                          </td>
                          <td style={{ width: "60px" }}>
                            <FontAwesomeIcon
                              data-tooltip-id={item["resume_present"] ? "random-tooltip" : "my-tooltip"}
                              data-tooltip-content="Resume not available"
                              icon={faFileAlt}
                              className={item["resume_present"] ? "resume_option" : "avoid_resume_option"}
                              style={{
                                color: item.resume ? "green" : "gray",
                                fontSize: "18px",
                              }}
                              onClick={() => item["resume_present"] && resumeApiCall(item)}
                            />
                            <ReactTooltip id="my-tooltip" place="bottom" variant="error" />
                          </td>
                          <td>
                            <FontAwesomeIcon
                              icon={faInfoCircle}
                              style={{ color: "#5E5C6C", cursor: "pointer", fontSize: "18px" }}
                              onClick={() => goToCandidateDetails(item)}
                            />
                          </td>
                          <td>
                            <FontAwesomeIcon
                              icon={faSyncAlt}
                              style={{ color: "#FEC601", cursor: "pointer", fontSize: "18px" }}
                              onClick={() => {
                                localStorage.setItem("page_no", id);
                                localStorage.setItem("isUpdated", false);
                                navigate("/UpdateCandidate", {
                                  state: { item, path: location.pathname },
                                });
                              }}
                            />
                          </td>
                          <td>
                            <FontAwesomeIcon
                              icon={faPen}
                              style={{ color: "#06908F", cursor: "pointer", fontSize: "18px" }}
                              onClick={() => {
                                localStorage.setItem("page_no", id);
                                goToEdit(item.id);
                              }}
                            />
                          </td>

                          <td>  
                            <FontAwesomeIcon
                              icon={faDownload}
                              style={{ color: "#06908F", cursor: "pointer", fontSize: "18px" }}
                              onClick={() => downloadAdditionalFiles(item)}
                            />
                          </td>
                          {USERTYPE === "managment" && (
                            <td>
                              <FontAwesomeIcon
                                icon={faTrashAlt}
                                style={{ color: "#E15554", cursor: "pointer", fontSize: "18px" }}
                                onClick={() => deleteFunction(item.id)}
                              />
                            </td>
                          )}
                        </tr>
                      );
                    })
                  )}
                </tbody>
              )}
            </table>

          </div>
        </div>

        <div
          style={{

          }}
          className="dashbottom"
        // className={`dashbottom ${isAnimating ? 'genie-effect' : ''}`}
        >
          <div>
            Showing {belowCount === 0 ? 0 : (id - 1) * 60 + 1} to{" "}
            {id * 60 <= belowCount ? id * 60 : belowCount} of {belowCount}{" "}
            entries
          </div>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              marginTop: "10px",
            }}
            className="pagination"
          >
            <ul
              className="page"
              style={{
                display: "flex",
                alignItems: "center",
                overflowX: "none", // Enable horizontal scrolling
                whiteSpace: "nowrap", // Prevent wrapping of child elements
                padding: "10px",
              }}
            >
              <li
                className="page__btn newpage_btn"
                style={{
                  padding: "1px 5px",
                  marginRight: "5px",
                  cursor: "pointer",
                  alignItems: "center",
                  color: "#32406d",
                }}
                onClick={() => {
                  if (id !== 1) {
                    setId(id - 1); // Go to previous page if not the first page
                  } else {
                    toast.warn("You have reached the starting page already.", {
                      position: "top-right",
                      autoClose: 3000,
                      hideProgressBar: false,
                      closeOnClick: true,
                      pauseOnHover: true,
                      draggable: true,
                      progress: undefined,
                      theme: "dark",
                      transition: Bounce,
                    }); // Show warning toast if already on first page
                  }
                }}
              >
                <FaAngleLeft style={{ marginTop: "3px" }} />
              </li>
              <div
                className="gap"
                style={{
                  display: "flex",
                  columnGap: "10px",
                  overflowX: "auto", // Enable horizontal scrolling for the page numbers
                  whiteSpace: "nowrap", // Prevent wrapping of child elements
                  padding: "10px 0",
                }}
              >
                {getPageRange().map((pageNumber, index) => (
                  <button
                    className={
                      pageNumber === id ? "pag_buttons" : "unsel_button"
                    }
                    key={index}
                    onClick={() => goToPage(pageNumber)}
                    style={{
                      fontWeight: pageNumber === id ? "bold" : "normal",
                      marginRight: "10px",
                      color: pageNumber === id ? "white" : "#000000", // Changed text color
                      backgroundColor:
                        pageNumber === id ? "#32406d" : "#ffff", // Changed background color
                      borderRadius: pageNumber === id ? "0.2rem" : "",
                      fontSize: "15px",
                      border: "none",
                      padding: "1px 10px", // Adjusted padding
                      cursor: "pointer", // Added cursor pointer
                    }}
                    class="page__numbers"
                  >
                    {pageNumber}
                  </button>
                ))}
              </div>
              <li
                className="page__btn newpage_btn"
                style={{
                  padding: "1px 5px",
                  cursor: "pointer",
                  color: "#32406d",
                  marginLeft: "3px",
                }}
                onClick={() => {
                  if (belowCount > id * 60) setId(id + 1);
                  else {
                    toast.warn("Reached the end of the list", {
                      position: "top-right",
                      autoClose: 3000,
                      hideProgressBar: false,
                      closeOnClick: true,
                      pauseOnHover: true,
                      draggable: true,
                      progress: undefined,
                      theme: "dark",
                      transition: Bounce,
                    });
                    setId(id);
                  }
                }}
              >
                <FaAngleRight style={{ marginTop: "3px" }} />
              </li>
            </ul>

          </div>
        </div>


      </div>


      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        style={{
          overlay: {
            backgroundColor: 'rgba(0, 0, 0, 0.75)',
            zIndex: 9999,
          },
          content: {
            color: 'lightsteelblue',
            top: '50%',
            left: '50%',
            right: 'auto',
            bottom: 'auto',
            marginRight: '-50%',
            transform: 'translate(-50%, -50%)',
            width: "450px",
            height: "150px"
          }
        }}>
        <div style={{ textAlign: 'center', marginTop: "20px" }}>
          <p>{modalMessage}</p>
        </div>
        <div style={{ display: 'flex', justifyContent: 'center', gap: '10px', width: '100%', marginTop: "30px" }}>
          <button
            onClick={() => {
              if (isSuccessful) {
                openModals();
              }
              setIsModalOpen(false);
            }}
            style={{
              color: "white",
              backgroundColor: "green",
              border: "none",
              width: "50px",
              height: "25px",
              borderRadius: "5px"
            }}
          >
            Ok
          </button>
          <button
            onClick={() => setIsModalOpen(false)}
            style={{
              color: "white",
              backgroundColor: "red",
              border: "none",
              width: "50px",
              height: "25px",
              borderRadius: "5px"
            }}
          >
            Close
          </button>
        </div>



      </Modal>
      <Modal
        isOpen={showModal}
        onRequestClose={handleCloseModal}
        contentLabel="Logout Confirmation"

        className="modal-content"
        overlayClassName="modal-overlay"
        style={{
          overlay: {
            backgroundColor: "rgba(0, 0, 0, 0.5)", // Transparnt background to show blurred content
            backdropFilter: "blur(0.5px)", // Blur effect for the entire screen
            zIndex: 9999,
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          },
          content: {
            width: "270px",
            height: "110px",
            margin: "auto",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            background: "white",
            borderRadius: "10px",
            boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.5)",
            padding: "20px 20px 10px",
          },
        }}
      >
        <div className="modal-actions" style={{ marginBottom: "10px" }}>
          <p
            style={{
              fontSize: "17px",
              fontFamily: "roboto",
              // fontWeight: "50",
              color: "black",
            }}
          >
            Are you sure you want to Delete?
          </p>
        </div>
        <div
          style={{
            width: "100%",
            display: "flex",
            justifyContent: "space-evenly",
          }}
        >
          {!waitForSubmission && (
            <button
              onClick={handleCloseModal}
              style={{
                backgroundColor: "#d90000",
                // marginRight: "30px",
                color: "white",
                height: "28px",
                borderRadius: "5px",
                border: "none",
                padding: "5px",
                cursor: "pointer",
                width: "50px",
              }}
            >
              No
            </button>
          )}
          <button
            onClick={handleDeleteCandidate}
            style={{
              // marginRight: "30px",
              backgroundColor: "green",
              color: "white",
              height: "28px",
              borderRadius: "5px",
              border: "none",
              padding: "5px",
              cursor: "pointer",
              width: waitForSubmission ? "70px" : "50px",
            }}
          >
            {!waitForSubmission ? (
              "Yes"
            ) : (
              <ThreeDots
                wrapperClass="ovalSpinner"
                wrapperStyle={{ marginTop: "-5px", marginLeft: "17px" }}
                visible={waitForSubmission}
                height="30"
                width="30"
                color="white"
                ariaLabel="oval-loading"
              />
            )}
          </button>
        </div>
      </Modal>
      <Modal
        isOpen={showModals}
        onRequestClose={newcloseModal}
        contentLabel="Selected Job Details"
        style={{
          overlay: {
            backgroundColor: "rgba(0, 0, 0, 0)", // Transparent background to show blurred content
            backdropFilter: "blur(0.5px)", // Blur effect for the entire screen
            zIndex: 9999,
            position: "fixed",
            top: 30,
            left: 0,
            right: 0,
            bottom: 0,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            marginLeft: "60px",
          },
          content: {
            backgroundColor: 'white',
            top: '50%',
            left: window.innerWidth <= 542 ? '40%' : '50%',
            right: 'auto',
            bottom: 'auto',
            marginRight: '-50%',
            transform: 'translate(-50%, -50%)',
            maxWidth: window.innerWidth <= 542 ? '100%' : '50vw', // Allow responsive sizing
            width: '100%',
            height: selectedJobDetails.length === 0 ? '30vh' : '93vh',
            overflow: "hidden"
          },
        }}
      >
        <h3 style={{ textAlign: "center" }}>Candidates Details</h3>

        {selectedJobDetails.length > 0 ? (
          <>
            {/* Display the client heading once */}
            <h3>{selectedJobDetails[0].client}</h3>
            <div
              style={{
                maxHeight: '70vh',
                overflowY: 'auto',
                overflowX: 'hidden',

              }}
            >
              {selectedJobDetails.map((job) => (
                <div key={job.id} style={{ marginBottom: '20px' }}>
                  <table style={{ width: '100%' }}>
                    <thead>
                      <tr>
                        <th colSpan="2" style={{ textAlign: 'left', padding: '5px' }}>
                          {job.name} <span style={{ color: '#888' }}>({job.id})</span>
                          <span
                            onClick={() => handleUnselectJob(job.id)}
                            style={{
                              color: 'red',
                              cursor: 'pointer',
                              fontSize: '20px',
                              marginLeft: '10px',
                            }}
                          >
                            &#10005;
                          </span>
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {Object.entries(job).map(([key, value]) => {
                        if (key === "job_id" || key === "id"||key === "comments") {
                          return null;
                        }
                        const isFieldSelected = selectedFields[key] !== undefined;
                        const displayValue = value ? value : " - ";

                        return (
                          <tr key={key}>
                            <th
                              onClick={() => handleSelectField(job.id, key, value)} // Toggle field selection on click
                              style={{
                                backgroundColor: isFieldSelected ? 'green' : '#cdcdcd',
                                color: isFieldSelected ? '#fff' : 'rgb(0,0,0)',
                                cursor: 'pointer',
                                padding: "2px 15px",
                                fontSize: "16px",
                                fontWeight: "500",
                                textAlign: "left",
                              }}
                            >
                              {key}:
                            </th>
                            <td
                              onClick={() => handleSelectField(job.id, key, value)} // Toggle field selection on click
                              style={{
                                backgroundColor: isFieldSelected ? 'green' : '#cdcdcd',
                                color: isFieldSelected ? '#fff' : 'rgb(0,0,0)',
                                cursor: 'pointer',
                                padding: "2px 15px",
                              }}
                            >
                              {displayValue}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              ))}
            </div>
          </>
        ) : (
          <p>No jobs selected</p>
        )}

        <div
          style={{
            position: 'sticky',
            display: 'flex',
            justifyContent: 'space-between',
            zIndex: 1000,
            padding: "10px",
          }}
        >
          <button
            onClick={newcloseModal}
            style={{
              backgroundColor: "red", // Red for Close
              color: "white",
              borderRadius: "4px",
              cursor: "pointer",
              border: "none",
              height: "40px",
              width: "100px",
            }}
          >
            Close
          </button>
          <button
            onClick={() => setShowEmailModal(true)} // Show the email modal
            style={{
              backgroundColor: "green", // Green for Share via Email
              color: "white",
              borderRadius: "4px",
              cursor: "pointer",
              border: "none",
              height: "40px",
              width: "140px",
            }}
          >
            Share via Email
          </button>
        </div>
      </Modal>

      <Modal
        isOpen={showEmailModal}
        onRequestClose={() => closeModal()}
        contentLabel="Recipient Email Selection"
        className="modal-content"
        overlayClassName="modal-overlay"
        style={{
          overlay: {

            zIndex: 9999,
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          },
          content: {
            width: "450px",
            height: "auto",
            maxHeight: "80vh", // Limit max height
            display: "flex",
            flexDirection: "column",
            background: "#ffffff",
            borderRadius: "10px",
            boxShadow: "0px 6px 20px rgba(0, 0, 0, 0.3)", // Softer shadow
            padding: "20px",
          },
        }}
      >
        <h2 style={{ margin: 0, marginBottom: '15px', textAlign: "left", fontWeight: '600', color: '#333' }}>Email Format</h2>
        <div style={{ marginBottom: '15px' }}>
          <label htmlFor="emailSubject" style={{ display: 'block', fontWeight: '500', marginBottom: '5px' }}>Subject</label>
          <input
            type="text"
            id="emailSubject"
            value={emailSubject}
            onChange={(e) => setEmailSubject(e.target.value)}
            placeholder="Enter subject"
            style={{
              width: '100%',
              padding: '10px',
              border: '1px solid #ddd',
              borderRadius: '4px',
              fontSize: '16px',
              outline: 'none',
            }}
          />
        </div>
        <div style={{ marginBottom: '15px' }}>
          <label htmlFor="emailSubject" style={{ display: 'block', fontWeight: '500', marginBottom: '5px' }}>Body Text</label>
          <input
            type="text"
            id="emailSubject"
            value={emailBody}
            onChange={(e) => setEmailBody(e.target.value)}
            placeholder="Enter subject"
            style={{
              width: '100%',
              padding: '10px',
              border: '1px solid #ddd',
              borderRadius: '4px',
              fontSize: '16px',
              outline: 'none',
              overflow: "auto"
            }}
          />
        </div>
        <div style={{ position: 'relative', marginBottom: '15px' }}>
          <label htmlFor="emailSubject" style={{ display: 'block', fontWeight: '500', marginBottom: '5px' }}>Email</label>
          <div style={{
            display: 'flex',
            flexWrap: 'nowrap',
            alignItems: 'center',
            border: '1px solid #ddd',
            borderRadius: '4px',
            padding: '5px',
            fontSize: '14px',
            overflowX: 'auto',
            height: '50px',
          }}>
            {selectedEmails2.map((email, index) => (
              <div key={index} style={{
                display: 'inline-flex',
                alignItems: 'center',
                backgroundColor: '#007bff',
                color: 'white',
                padding: '5px 10px',
                borderRadius: '20px',
                marginRight: '5px',
              }}>
                {email}
                <span onClick={() => handleEmailChanges(email)} style={{
                  marginLeft: '8px',
                  cursor: 'pointer',
                  fontWeight: 'bold',
                }}>&times;</span>
              </div>
            ))}
            <div style={{ display: 'flex', alignItems: 'center', flex: '1' }}>
              <input
                type="text"
                placeholder="Search or add email"
                onChange={(e) => setSearchQuery2(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleEmailChanges(searchQuery2);
                    setSearchQuery2('');
                  }
                }}
                value={searchQuery2}
                style={{
                  flex: '1',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  paddingLeft: '10px',
                  fontSize: '16px',
                  outline: 'none'
                }}
                ref={inputRef2}
              />
              {searchQuery2 && ![...managers, ...recruiters].some((item) => item.email.toLowerCase().includes(searchQuery2.toLowerCase())) && (
                <button
                  type="button"
                  onClick={() => handleEmailChanges(searchQuery2)}
                  style={{
                    backgroundColor: '#007bff',
                    color: 'white',
                    border: 'none',

                    padding: '5px 10px',
                    borderRadius: '4px',
                    marginLeft: '5px',
                  }}
                >
                  Add
                </button>
              )}
            </div>
          </div>

          {searchQuery2 && (
            <div className="dropdown-menu" style={{
              border: '1px solid #ddd',
              borderRadius: '4px',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',
              marginTop: '5px',
              width: "100%",
              backgroundColor: 'white', // Ensure dropdown has a background
              zIndex: 1000, // Ensure it's above other elements
            }}>
              {[...new Set([
                ...managers.map(item => item.email.toLowerCase()),
                ...recruiters.map(item => item.email.toLowerCase())
              ])]
                .filter(email => email.includes(searchQuery2.toLowerCase()) && email !== loggedInEmail)
                .map((email, index) => (
                  <label key={index} className="dropdown-item" style={{ display: 'block', padding: '8px', fontWeight: "normal", cursor: 'pointer' }}>
                    <input
                      type="checkbox"
                      checked={selectedEmails2.includes(email)}
                      onChange={() => handleEmailChanges(email)}
                      style={{ marginRight: '8px' }}
                    />
                    {email}
                  </label>
                ))}
            </div>
          )}
        </div>

        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <button
            onClick={() => {
              setShowEmailModal(false)

            }}
            style={{
              backgroundColor: '#dc3545', // Red for cancel
              color: 'white',
              border: 'none',
              padding: '10px 15px',
              borderRadius: '4px',
              cursor: 'pointer',
              transition: 'background-color 0.3s',
            }}
          >
            Cancel
          </button>
          <button
            onClick={handleEmailSend}
            style={{
              backgroundColor: '#28a745', // Green for success
              color: 'white',
              border: 'none',
              padding: '10px 15px',
              borderRadius: '4px',
              marginRight: '10px',
              cursor: 'pointer',
              transition: 'background-color 0.3s',
            }}
          >
            send
          </button>

        </div>
      </Modal>
      <Modal
        isOpen={showEmailpasswordModal}
        onRequestClose={() => closepasswordModal()}
        contentLabel="Recipient Email Selection"
        className="modal-content"
        overlayClassName="modal-overlay"
        style={{
          overlay: {

            zIndex: 9999,
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          },
          content: {
            width: "450px",
            height: "auto",
            maxHeight: "80vh", // Limit max height
            display: "flex",
            flexDirection: "column",
            background: "#ffffff",
            borderRadius: "10px",
            boxShadow: "0px 6px 20px rgba(0, 0, 0, 0.3)", // Softer shadow
            padding: "20px",
          },
        }}
      >
        <h2 style={{ margin: 0, marginBottom: '15px', textAlign: "center", fontWeight: '600', color: '#333' }}>
          Microsoft Password  for Authentication</h2>

        <div style={{ position: 'relative', marginBottom: '15px' }}>
          <div style={{
            display: 'flex',
            flexWrap: 'nowrap',
            alignItems: 'center',

            borderRadius: '4px',
            padding: '5px',
            fontSize: '14px',
            overflowX: 'auto',
            height: '50px',
          }}>

            <div style={{ display: 'flex', alignItems: 'center', flex: '1' }}>
              <input
                type={showNewPassword ? "text" : "password"}
                placeholder=" Password"
                // placeholder="Search or add email"
                style={{
                  flex: '1',
                  border: '1px solid #cdcd',
                  borderRadius: '4px',
                  padding: '5px',
                  paddingLeft: '10px',
                  fontSize: '16px',
                  outline: 'none'
                }}

                value={Emailpassword}
                onChange={(e) => setEmailpassword(e.target.value)}
              />

              <span
                className="password-toggle"
                onClick={() => togglePasswordVisibility("newPassword")}
                style={{
                  position: "relative",
                  top: "2px",
                  left: "-8%",
                  //   top:
                  //   window.innerWidth <= 542
                  //     ? errors.newPassword
                  //       ? "44%"
                  //       : "46%"
                  //     : errors.newPassword
                  //     ? "48%"
                  //     : "49%",
                  right: "3px",
                  cursor: "pointer",
                  fontSize: "20px",
                }}
              >
                {showNewPassword ? <RiEyeLine /> : <RiEyeOffLine />}
              </span>
            </div>

          </div>
          <em style={{ color: "red", fontSize: "15px" }}>*Password is not saved for Security Reason*</em>
        </div>

        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <button
            onClick={() => {
              setshowEmailpasswordModal(false)
              setSelectedJobs([]);
              setSelectedFields([]);
              setSelectedJobDetails([])
              setEmailpassword('')
              setSearchQuery2('')
              setSelectedEmails2([]);
              setEmailSubject("")
              setEmailBody("")
            }}
            style={{
              backgroundColor: '#dc3545', // Red for cancel
              color: 'white',
              border: 'none',
              padding: '10px 15px',
              borderRadius: '4px',
              cursor: 'pointer',
              transition: 'background-color 0.3s',
            }}
          >
            Cancel
          </button>
          <button
            onClick={handleEmailSendconfrim}
            style={{
              backgroundColor: '#28a745', // Green for success
              color: 'white',
              border: 'none',
              padding: '10px 15px',
              height: "40px",
              borderRadius: '4px',
              marginRight: '10px',
              cursor: 'pointer',
              transition: 'background-color 0.3s',
            }}
          >
            {waitForSubmissionemail ? "" : "Confirm"}

            <ThreeDots
              wrapperClass="ovalSpinner"
              wrapperStyle={{
                position: "relative",
                top: "-11px",
                left: "0px",
                bottom: "-3px",
              }}
              visible={waitForSubmissionemail}
              height="45"
              width="45"
              color="white"
              ariaLabel="oval-loading"
            />
          </button>

        </div>
      </Modal>

    </div>
  );
}
export default DashBoard;
