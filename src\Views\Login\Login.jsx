import React, { useEffect } from "react";
import { Modal } from "bootstrap";
import { <PERSON> } from "react-router-dom";
import { useLocation, useNavigate } from "react-router-dom";
import "./Login.css";
//import <PERSON><PERSON> from "../../assets/Mako.png";
import <PERSON><PERSON> from "../../assets/home page.png";
import Logo from "../../assets/Logo.png";
import { useDispatch } from "react-redux";
import { setDashboardData } from "../../store/slices/dashboardSlice";
const Login = () => {
  const dispatch = useDispatch();
  const { pathname } = useLocation();
  useEffect(() => {
    const modalElement = document.getElementById("myModal");
    if (modalElement) {
      const myModal = new Modal(modalElement);
      myModal.show();
    }
  }, []);
  useEffect(() => {
    localStorage.removeItem("user_id");
    localStorage.removeItem("user_type");
    localStorage.removeItem("user_name");
    dispatch(setDashboardData({ data: {} }));
  }, []);
  return (
    <html>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>index</title>
      </head>
      <body>
        <img className="background" src="https://assets.entrepreneur.com/content/3x2/2000/1716219501-ai-applicant-tracking-system-0524-1479275024.jpg?format=pjeg&auto=webp&crop=16:9" />
        {/* <div className="logo-div">
          <img className="logo" src={Logo} alt="logo" />
        </div> */}
        {/* <div className="top-nav" style={{ marginLeft: "5%" }}>
          <h1 style={{ fontSize: "35px", fontFamily: "Poppins, sans-serif", fontWeight: "700", color: "#fff" }}>Makonis Talent Track Pro</h1>
        </div>
         <div className="logedmob" style={{ marginLeft: "0%" }}>


          <div className="top-right">
             <a href="http://142.93.222.128/Register"  rel="noopener noreferrer">
              <button className="R">Register</button>
            </a>
          </div>
          <div className="login-links">
          <a href="http://142.93.222.128/RecruitmentLogin"  rel="noopener noreferrer">
              <button className="R">Recruiter Login</button>
            </a>
            <a href="http://142.93.222.128/ManagementLogin" target="_blank" rel="noopener noreferrer">
              <button className="R">Manager Login</button>
            </a>
          </div>
        </div>  */}
       <div className="logedmob" style={{ marginLeft: "0%" }}>
          <div className="top-right">
            <Link to="/Register">
              <button className="R">Register</button>
            </Link>
          </div>
          <div className="login-links">
            <Link to="/RecruitmentLogin">
              <button className="R">Recruiter Login</button>
            </Link>
            <Link to="/ManagementLogin">
              <button className="R">Manager Login</button>
            </Link>
          </div>
        </div> 
      </body>
    </html>
  );
};

export default Login;
