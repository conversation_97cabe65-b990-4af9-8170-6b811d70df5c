@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@200;300;400;500;600&display=swap");

.users {
  display: flex;
  padding-top: 40px;
  font-weight: 700;
  font-size: 18px;
  text-align: center;
  justify-content: center;
  color: #000000;
}
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 26px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.sliderAccountDeactivation {
  position: absolute;
  cursor: pointer;
  top: 8px;
  left: -1px;
  right: 6px;
  bottom: -2px;
  /* background-color: #008000; */
  background-color: grey;
  transition: 0.4s;
}

.sliderAccountDeactivation:before {
  position: absolute;
  content: "";
  height: 17px;
  width: 17px;
  border-radius: 50%;
  background-color: white;
  transition: 0.4s;
  top: 1.5px;
  left: 1px;
}

input:checked + .sliderAccountDeactivation {
  background-color: green;
}

input:checked + .sliderAccountDeactivation:before {
  transform: translateX(25px);
}

.sliderAccountDeactivation.round {
  border-radius: 26px;
}

.sliderAccountDeactivation.round:before {
  border-radius: 50%;
}
.userac tbody tr {
  border-bottom: 2px solid yellow;
}
.container .userac thead {
  height: 5px;
}
.container{
  flex:1;
}
.table {
  width: 100%;
  border-collapse: collapse; /* Ensures borders are displayed as expected */
}

.userac tbody tr {
  border-bottom: 1px solid rgba(105, 105, 105, 0.856); /* Applies a border to each row */
}
.userac thead tr th {
  font-size: 14px;
  padding: 10px 2px;
}
.userac tbody tr td {
  border-right: none;
  font-size: 14px; /* Applies a border to each row */
}

.table tbody tr:nth-child(odd) {
  background-color: #f9f9f9; /* Applies alternating row colors */
}
.td_UA td {
  border: 1px solid #ddd;
  height: 35px;
  text-align: left;
}
.UAsearch{
  margin-top: 4px;
  padding-left: 26px;
  height: 30px;
  width: 250px;
  background-color: rgba(255, 255, 255, 0.80);
  border: none;
  border-radius:5px;
  padding: 0 25px
}
@media screen and (min-width:320px) and (max-width:375px) {
  .UAsearch{
    width: 200px !important;
}
}
@media screen and (min-width:375px) and (max-width:425px){
  .UAsearch{
    width: 250px  !important;
}
}

/*  table scroll or the acco deactivstion */

/* table {
  width: 100%;
  border-collapse: collapse;
}

thead {
  position: sticky;
  top: 0;
  background-color: white; 
  z-index: 2;
}

tbody {
  display: block;
  max-height: 300px; 
  overflow-y: auto;
  width: 100%;
}

tbody tr {
  display: table;
  width: 100%;
  table-layout: fixed;
} */

/*   this is extend material ui design */
/* .css-1tbggly{
  background-color: #1d1d1d !important;
}
.css-12yjm75-MuiInputBase-input-MuiOutlinedInput-input{
  color:grey !important;
}
.css-i4bv87-MuiSvgIcon-root {
  fill: #fff !important;
}
.css-1fwjva3{
  background-color: #1d1d1d !important;
}
.css-hsi95o-MuiTableRow-root td {
  overflow-y: auto;
  background-color:#1d1d1d;
  color:#fff
}
thead tr th {
  background-color: #1d1d1d !important;
  color:#fff !important;
} */

/* material ui  */

.css-q5fqw0{
 background-color: #fff;
 /* display: none; */
}

/* .Filter-popup {
  z-index: 1000;
  display: flex;
  flex-direction: column;
  position: absolute;
  background-color: #b3b3b3;
  border: 1px solid #ccc;
  border-radius: 3px;
  padding: 0 !important;
  max-width: 300px;
  overflow: auto;
  max-height: 183px;
  height: auto;
  margin-top: 230px;
   margin-left: 60%; 
} */

.MuiTableHead-root {
  position: sticky;
  top: 0;
  background-color: yellow !important; 
  color: white !important;
  z-index: 1000;
}
/*
.MuiTableCell-root {
  background-color: #cce5ff !important; 
  color: black !important;
} */