
import React, { useState, useRef, memo, useEffect, useMemo } from "react";
import Modal from "react-modal";
import { useDispatch } from "react-redux";
import { IoMdAttach } from "react-icons/io";
import { useSelector } from "react-redux";
import { ThreeDots } from "react-loader-spinner";
import { setMeetings, setError } from "../store/slices/meetingslice";
import { getDashboardData } from "../Views/utilities";
import { fetchMeetings } from "../Views/utilities";
import { toast } from "react-toastify";


const ScheduleMeet = ({ interviewModal, InterviewcloseModal, start_autoDate,
  end_autoDate,
  startautoTime,
  endautoTime, setShowCalendar, selectedScheduleData }) => {

  const dispatch = useDispatch();

  const { dashboardData } = useSelector((state) => state.dashboardSliceReducer);

  const candidateEmails = dashboardData.candidates?.map(candidates => candidates.email) || [];


//  console.log(candidateEmails, "candidate emails");

  

  const { managers } = useSelector((state) => state.userSliceReducer);
  if (Array.isArray(managers)) {
    const emails = managers.map(manager => manager.email);
    // console.log(emails, "emails");
  } else {
    console.log("Managers is not an array or is empty");
  }
  // console.log(managers, "managers");
  const { recruiters } = useSelector((state) => state.userSliceReducer);
  if (Array.isArray(recruiters)) {
    const recruiteremails = recruiters.map(recruiters => recruiters.email);
    // console.log(recruiteremails, "recruiteremails");
  } else {
    console.log("recruiters is not an array or is empty");
  }
  // console.log(recruiters, "recruiters");
  const [title, setTitle] = useState('');
  const [selectedEmails1, setSelectedEmails1] = useState([]);
  const [selectedEmails2, setSelectedEmails2] = useState([]);
  const [isDropdownOpen, setIsDropdownOpen] = useState(null);
  const [inputValue, setInputValue] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  // const [start_Date, setStartDate] = useState(start_autoDate || "");
  // const [end_Date, setEndDate] = useState(end_autoDate || "");;
  const [startTime, setStartTime] = useState(startautoTime || "");;
  const [endTime, setEndTime] = useState(endautoTime || "");;
  const [isSuccessful, setIsSuccessful] = useState(false);
  const [responseSuccess, setResponseSuccess] = useState(false);



  useEffect(() => {
    if (start_autoDate) setStartDate(start_autoDate);
    if (end_autoDate) setEndDate(end_autoDate);
    if (startautoTime) setStartTime(startautoTime);
    if (endautoTime) setEndTime(endautoTime);
  }, [start_autoDate, end_autoDate, startautoTime, endautoTime]);


  // console.log(start_autoDate,"start Date")
  // console.log(end_autoDate,"End Date")
  // console.log(startTime,"Start Time")
  // console.log(endTime,"End Time")
  // console.log("----------------------------------------------------------------------------------------------------------")

  const dropdownRef1 = useRef(null);
  const dropdownRef2 = useRef(null);
  const inputRef1 = useRef(null);
  const inputRef2 = useRef(null);
  const dropdownRef = useRef(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMessage, setModalMessage] = useState('');
  const [selectedEmails, setSelectedEmails] = useState([]);

  const [waitForSubmission1, setwaitForSubmission1] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddButton, setShowAddButton] = useState(false);
  const [newfilteredEmails, setnewFilteredEmails] = useState([]);
  const loggedInEmail = localStorage.getItem('email').toLowerCase();

  const handleSearch = (e) => {
    const query = e.target.value;
    setSearchQuery(query);
    const filtered = candidateEmails.filter((email) =>
      email.toLowerCase().includes(query.toLowerCase())
    );
    setnewFilteredEmails(filtered);
    setShowAddButton(query && filtered.length === 0);
  };

  const handleAddEmail = () => {
    const trimmedQuery = searchQuery.trim();
    if (trimmedQuery && !selectedEmails.includes(trimmedQuery)) {
      setSelectedEmails([...selectedEmails, trimmedQuery]);
    }
    setSearchQuery('');
    setShowAddButton(false);
  };

  // console.log(candidateEmails, ":allcandidateschedule emails")
  const filteredEmails = candidateEmails.filter((email) =>
    email.toLowerCase().includes(searchQuery.toLowerCase())
  );
  //  console.log(filteredEmails, ":allcandidateschedule emails")

  const handleDropdownClick = (dropdown) => {
    setIsDropdownOpen(isDropdownOpen === dropdown ? null : dropdown);
  };
  const handleEmailChange = (email, dropdown) => {
    if (dropdown === 'dropdown1') {
      setSelectedEmails1(prev => {
        if (!Array.isArray(prev)) return [email]; // Ensure prev is an array
        return prev.includes(email)
          ? prev.filter(e => e !== email)
          : [...prev, email];
      });
    } else if (dropdown === 'dropdown2') {
      setSelectedEmails2(prev => {
        if (!Array.isArray(prev)) return [email]; // Ensure prev is an array
        return prev.includes(email)
          ? prev.filter(e => e !== email)
          : [...prev, email];
      });
      setSearchQuery2('');
      setIsDropdownOpen2(false);
    }
  };

  const handleTimeZoneChange = (e) => {
    const newTimeZone = e.target.value;
    setSelectedTimeZone(newTimeZone);
    console.log("Selected Time Zone:", newTimeZone); // Log the new value
  };
  const [selectedTimeZone, setSelectedTimeZone] = useState('');

  const syncEvents = async () => {
    try {
      const response = await fetch('http://************:5002/sync_events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          recruiter_email: localStorage.getItem('email'),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to sync events');
      }

      const data = await response.json();
      console.log('Sync events response:', data);
    } catch (error) {
      console.error('Sync error:', error);
      setError(error.message);
    }
  };
  const [description, setDescription] = useState('');
  const handleDescriptionChange = (e) => {
    setDescription(e.target.value);
  };
  const [files, setFiles] = useState([]);
  const fileToBase64 = file => new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const result = reader.result;
      const base64 = result.split(',')[1];
      resolve(base64);
    };
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
  const handleFileChange = e => {
    setFiles(Array.from(e.target.files));
  };
    const handleRemoveFile = (indexToRemove) => {
    const updatedFiles = files.filter((_, index) => index !== indexToRemove);
    setFiles(updatedFiles);
   
  };
  //  console.log(fileToBase64, "fileToBase64");
  // console.log(selectedScheduleData,"ddwqfwwrwr")
  useEffect(() => {
    if (selectedScheduleData) {
      // If user clicked from dashboard, prefill title

      console.log(selectedScheduleData.profile, "daafwuw")
      console.log(selectedScheduleData.client, "daafwuw")
      console.log(selectedScheduleData.status, "daafwuw")
      setTitle(`Meeting | ${selectedScheduleData.client || ''} | ${selectedScheduleData.profile || ''} | ${selectedScheduleData.status || ''}`);
    } else {
      // If user clicked from calendar, allow manual typing
      setTitle('');
    }
  }, [selectedScheduleData]);
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!title || !startDate || !endDate || !startTime || !endTime || !selectedTimeZone || selectedEmails.length === 0) {
      toast.error('Please fill in all required fields.');
      return;
    }
    const requiredAttendees = selectedEmails;
    const optionalAttendees = selectedEmails2;
    // const attachments = await Promise.all(
    //   files.map(async file => ({
    //     name: file.name,
    //     content_type: file.type,
    //     content_bytes: await fileToBase64(file)
    //   }))
    // );

    const filePayload = await Promise.all(
      files.map(async (file) => ({
        file_name: file.name,
        file_content_base64: await fileToBase64(file),
      }))
    );

    console.log(filePayload, "attachments");
    if (!waitForSubmission1) {
      setwaitForSubmission1(true);
      try {
        const response = await fetch('http://************:5002/create_event', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer YOUR_ACCESS_TOKEN`,
          },
          body: JSON.stringify({
            subject: title,
            start_date: startDate,
            start_time: startTime,
            end_date: endDate,
            end_time: endTime,
            attendees: requiredAttendees,
            cc_recipients: optionalAttendees,
            time_zone: selectedTimeZone,
            recruiter_email: localStorage.getItem('email'),
            recruiter_id: localStorage.getItem("user_id"),
            description: description,
            files: filePayload,
            // attachments
          }),
        });

        if (response.ok) {
          setModalMessage('Meeting scheduled successfully!');
          setIsModalOpen(true);
          InterviewcloseModal();
          setwaitForSubmission1(false)
          setIsSuccessful(true)
          setTitle("");
          setStartDate("");
          setEndDate("");
          setStartTime("");
          setEndTime("");
          setFiles([])
          setSelectedTimeZone("");
          setSelectedEmails1([]);
          setSelectedEmails2([]);
          setDescription('');
          setResponseSuccess(true);
          await syncEvents();
          await fetchMeetings();
        } else {
          setModalMessage('Failed to schedule the meeting.');
          setIsModalOpen(true);
          setwaitForSubmission1(false)
          setIsSuccessful(false)
          setResponseSuccess(false);
        }
      } catch (error) {
        setModalMessage('An error occurred while scheduling the meeting.');
        setIsModalOpen(true);
        setwaitForSubmission1(false)
        setIsSuccessful(false)
        setResponseSuccess(false);
        console.error('Error:', error);
      }
    }
  };

  // useEffect(() => {
  //   const initialize = async () => {
  //     await syncEvents();
  //     fetchMeetings();
  //   };

  //   initialize();
  // }, []);


  const [startDate, setStartDate] = useState(() => {
    const today = new Date().toISOString().split('T')[0];
    return today;
  });
  const [endDate, setEndDate] = useState("");
  const [error, seterror] = useState('');
  useEffect(() => {
    if (startDate) {
      setEndDate(startDate);
    }
  }, [startDate]);

  const timeOptions = Array.from({ length: 24 * 2 }, (_, i) => {
    const hour = Math.floor(i / 2);
    const minute = (i % 2) * 30;
    return {
      value: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
      label: `${hour}:${minute.toString().padStart(2, '0')}`
    };
  });

  const add30Minutes = (time) => {
    const [hours, minutes] = time.split(':').map(Number);
    let newMinutes = minutes + 30;
    let newHours = hours;

    if (newMinutes >= 60) {
      newMinutes -= 60;
      newHours += 1;
    }

    if (newHours === 24) {
      newHours = 0;
    }

    return `${String(newHours).padStart(2, '0')}:${String(newMinutes).padStart(2, '0')}`;
  };

  useEffect(() => {
    if (startTime) {
      setEndTime(add30Minutes(startTime));
    } else {
      setEndTime('');
    }
  }, [startTime]);


  const [searchQuery2, setSearchQuery2] = useState('');

  // const handleSearch = (e) => {
  //   const query = e.target.value;
  //   setSearchQuery(query);
  //   const filtered = uniqueDataEmail.filter((email) =>
  //     email.toLowerCase().includes(query.toLowerCase())
  //   );
  //   setnewFilteredEmails(filtered);
  //   setShowAddButton(query && filtered.length === 0);
  // };

  // const handleAddEmail = () => {
  //   const trimmedQuery = searchQuery.trim();
  //   if (trimmedQuery && !selectedEmails.includes(trimmedQuery)) {
  //     setSelectedEmails([...selectedEmails, trimmedQuery]);
  //   }
  //   setSearchQuery('');
  //   setShowAddButton(false);
  // };

  // const handleKeyDown = (e) => {
  //   if (e.key === 'Enter') {
  //     handleAddEmail();
  //   }
  // };

  // const handleRemoveEmail = (emailToRemove) => {
  //   setSelectedEmails(selectedEmails.filter(email => email !== emailToRemove));
  // };

  const handleCheckboxChangeEmails = (email) => {
    if (!selectedEmails.includes(email)) {
      setSelectedEmails([...selectedEmails, email]);
    } else {
      setSelectedEmails(selectedEmails.filter((e) => e !== email));
    }
    setSearchQuery(''); // Clear search query
    setIsDropdownOpen(false); // Close the dropdown
    setDropdownVisible(false); // Ensure dropdown is closed
  };

  // const handleSelectCheckedEmails = () => {
  //   const newSelectedEmails = Object.keys(checkedEmails).filter(email => checkedEmails[email]);
  //   setSelectedEmails(prevSelectedEmails => [...new Set([...prevSelectedEmails, ...newSelectedEmails])]); // Add checked emails to selectedEmails
  //   setSearchQuery('');
  //   // setCheckedEmails({});
  // };

  // const filteredEmails = uniqueDataEmail.filter((email) =>
  //   email.toLowerCase().includes(searchQuery.toLowerCase())
  // );

  // const handleManualAdd = () => {
  //   handleEmailChange(searchQuery2, 'dropdown2');
  //   setSearchQuery2(''); // Clear the search input after adding the email
  //   setIsDropdownOpen(false); // Close the dropdown
  //   setDropdownVisible(false); // Ensure dropdown is closed

  // };

  // const filteredManagers = Array.isArray(managers)
  //   ? managers.filter(manager => manager.email.toLowerCase().includes(searchQuery2.toLowerCase()))
  //   : [];

  // const handleEmailChange1 = (email) => {
  //   setSelectedEmails(prevSelectedEmails => {
  //     let updatedEmails;
  //     if (prevSelectedEmails.includes(email)) {
  //       updatedEmails = prevSelectedEmails.filter(e => e !== email);
  //     } else {
  //       updatedEmails = [...prevSelectedEmails, email];
  //     }
  //     // Update the input field with the selected emails
  //     setSearchValue(updatedEmails.join(', '));
  //     return updatedEmails;
  //   });
  // };
  const handleStartDateChange = (e) => {
    const selectedStartDate = new Date(e.target.value);
    const today = new Date();

    // Set the time to midnight for accurate comparison
    today.setHours(0, 0, 0, 0);
    selectedStartDate.setHours(0, 0, 0, 0);

    // Get current year and month
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth();

    // Get the selected year and month
    const selectedYear = selectedStartDate.getFullYear();
    const selectedMonth = selectedStartDate.getMonth();

    // Validation
    if (
      selectedStartDate < today || // Cannot be in the past
      (selectedYear < currentYear || // Cannot be from a previous year
        (selectedYear === currentYear && selectedMonth < currentMonth)) // Cannot be from a previous month in the current year
    ) {
      setError('Start Date must be today or a future date in this month, next month, or next year.');
      toast.error('Start Date must be today or a future date in this month, next month, or next year.');
    } else {
      setError('');
      setStartDate(e.target.value);
    }
  };

  const handleEndDateChange = (e) => {
    const newEndDate = e.target.value;
    setEndDate(newEndDate);

    if (new Date(startDate) > new Date(newEndDate)) {
      seterror('End Date is Earlier than Start Date .');
      toast.error('End Date is Earlier than Start Date.');
    } else {
      seterror(''); // Clear error if validation passes
    }

  };

  const resetForm = () => {
    setTitle('');
    setSelectedEmails([]);
    setSearchQuery('');
    setSearchQuery2('');
    setSelectedEmails2([]);
    setSelectedTimeZone('');
    setError('');
    setFiles([])
    setStartTime('');
    setEndTime('');
    setStartDate('');
    setEndDate('');
  };
  const handleModalClose = () => {
    InterviewcloseModal();
    resetForm();
  };
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(\.[a-zA-Z]{2,})*$/;


  // Function to validate email before adding



  const validateEmail = (email) => {
    return emailRegex.test(email);
  };

  const handleAddEmailWithValidation = () => {
    if (validateEmail(searchQuery)) {
      handleAddEmail();
    } else {
      toast.warn('Please enter a valid email address');
    }
  };
  const handleAddEmailWithValidations = () => {
    if (validateEmail(searchQuery2)) {
      handleEmailChange(searchQuery2, 'dropdown2');
      setSearchQuery2('');
    } else {
      toast.warn('Please enter a valid email address');
    }
  };
  //  console.log(selectedScheduleData,"passing emails")
  // useEffect(() => {
  //   if (selectedScheduleData?.attendees && selectedScheduleData.attendees.length > 0) {
  //     const uniqueEmails = [...new Set([...selectedEmails, ...data.attendees])];
  //     console.log(uniqueEmails,"passing emails")
  //     setSelectedEmails(uniqueEmails);
  //     setSearchQuery(selectedScheduleData.attendees[0] || "");
  //   }
  // }, [selectedScheduleData]);

  useEffect(() => {
    if (selectedScheduleData?.email) {
      const email = selectedScheduleData.email.toLowerCase();
      // Avoid duplicates
      if (!selectedEmails.includes(email)) {
        setSelectedEmails(prev => [...prev, email]);
      }
      // setSearchQuery(email); // Pre-fill search box with the selected email
    }
  }, [selectedScheduleData]);
  return (
    <div>
      <Modal
        isOpen={interviewModal}
        onRequestClose={handleModalClose}
        contentLabel="Calendar Modal"
        style={{
          overlay: {
            backgroundColor: 'rgba(0, 0, 0, 0.5)', // Slightly lighter overlay
            zIndex: 9999,
          },
          content: {
            color: '#333', // Darker text for better readability
            top: '50%',
            left: '50%',
            right: 'auto',
            bottom: 'auto',
            marginRight: '-50%',
            transform: 'translate(-50%, -50%)',

            width: window.innerWidth <= 542 ? '100%' : '550px',
            maxHeight: '630px', // Restrict maximum height
            padding: '0px',
            borderRadius: '8px', // Rounded corners
            boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)', // Light shadow for depth
            position: 'relative', // For absolute positioning of buttons
          }
        }}
      >

        <form onSubmit={handleSubmit}>
          <div style={{ padding: "10px 15px" }}>
            <div className="shedulehed">
              <h2 style={{ color: "#32406D", fontSize: '20px', textAlign: 'center', }}>New Meeting</h2>

            </div>
            <div style={{ marginBottom: '5px' }}>
              <label style={{ display: 'block', marginBottom: '4px', fontSize: '16px', textAlign: "left" }}>Title</label>
              <input
                type="text"
                placeholder="Add title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                style={{ width: '100%', height: "40px", borderRadius: "4px", border: "1px solid #ccc", paddingLeft: "10px", fontSize: '16px' }}
              />
            </div>
            <div style={{ marginBottom: '5px' }}>
              <label style={{ display: 'block', marginBottom: '4px', fontSize: '16px', textAlign: "left" }}>Attendees</label>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                flexWrap: 'nowrap',
                border: '1px solid #ccc',
                borderRadius: '4px',
                padding: '5px',
                overflowX: 'auto',
                   overflowY: 'hidden',
                whiteSpace: 'nowrap',
                fontSize: '12px',
                height: '40px',
                position: 'sticky',
                right: '0px'
              }}>
                {selectedEmails.map((email, index) => (
                  <div
                    key={index}
                    style={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      backgroundColor: '#007bff',
                      color: 'white',
                      padding: '5px 10px',
                      borderRadius: '20px',
                      marginRight: '5px',
                    }}
                  >
                    {email}
                    <span
                      onClick={() => setSelectedEmails(selectedEmails.filter((e) => e !== email))}
                      style={{
                        marginLeft: '8px',
                        cursor: 'pointer',
                        fontWeight: 'bold',
                      }}
                    >
                      &times;
                    </span>
                  </div>
                ))}
                <div style={{ display: 'flex', alignItems: 'center', flex: '1', position: 'relative' }}>
                  <input
                    type="text"
                    placeholder="Search or add email"
                    value={searchQuery}
                    onChange={handleSearch}
                    style={{
                      flex: '1',
                      minWidth: '200px',
                      border: 'none',
                      paddingLeft: '10px',
                      fontSize: '16px',
                      outline: 'none',
                      width: 'auto'
                    }}
                  />
                  {searchQuery && filteredEmails.length === 0 && (
                    <button
                      type="button"
                      onClick={handleAddEmailWithValidation}
                      style={{
                        position: 'sticky',
                        right: '0px',
                        backgroundColor: '#007bff',
                        color: 'white',
                        border: 'none',
                        padding: '5px 10px',
                        borderRadius: '4px',
                        whiteSpace: 'nowrap',
                        zIndex: 1
                      }}
                    >
                      Add
                    </button>
                  )}
                </div>
              </div>

              {/* Display the email dropdown when there's a search query */}
              {searchQuery && (
                <div className="dropdown-menu" style={{
                  border: '1px solid #ccc',
                  borderRadius: '4px',
                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
                  marginTop: '5px',
                  width: "auto"
                }}>
                  {/* Combine managers and recruiters emails with existing filtered emails */}
                  {[...new Set([
                    // ...managers.map(item => item.email.toLowerCase()),
                    // ...recruiters.map(item => item.email.toLowerCase()),
                    ...filteredEmails.map(email => email.toLowerCase()) // add any filtered emails too
                  ])]
                    .filter(email => email.includes(searchQuery.toLowerCase()) && email !== loggedInEmail) // Exclude logged-in email
                    .map((email, index) => (
                      <label key={index} className="dropdown-item" style={{
                        display: 'flex',
                        padding: '8px',
                        fontWeight: "normal",
                        cursor: 'pointer'
                      }} onClick={() => {
                        handleCheckboxChangeEmails(email)
                        handleSelectCheckedEmails();
                      }}>
                        <input
                          type="checkbox"
                          checked={selectedEmails.includes(email)}
                          onChange={() => handleEmailChange(email, 'attendees')}
                          style={{ marginRight: '8px' }}
                        />
                        {email}
                      </label>
                    ))
                  }
                </div>
              )}
            </div>

            <div style={{ marginBottom: '5px' }}>
              <label style={{ display: 'block', marginBottom: '4px', fontSize: '16px', textAlign: "left" }}>Attendees (optional)</label>
              <div ref={dropdownRef2} style={{ position: 'relative' }}>
                <div style={{
                  display: 'flex',
                  flexWrap: 'nowrap',
                  alignItems: 'center',
                  border: '1px solid #ccc',
                  borderRadius: '4px',
                  padding: '5px',
                  fontSize: '12px',
                  position: 'relative',
                  overflowX: 'auto',
                  height: '40px'
                }}>
                  {selectedEmails2?.map((email, index) => (
                    <div
                      key={index}
                      style={{
                        display: 'inline-flex',
                        alignItems: 'center',
                        backgroundColor: '#007bff',
                        color: 'white',
                        padding: '5px 10px',
                        borderRadius: '20px',
                        marginRight: '5px',
                      }}
                    >
                      {email}
                      <span
                        onClick={() => handleEmailChange(email, 'dropdown2')}
                        style={{
                          marginLeft: '8px',
                          cursor: 'pointer',
                          fontWeight: 'bold',
                        }}
                      >
                        &times;
                      </span>
                    </div>
                  ))}
                  <div style={{ display: 'flex', alignItems: 'center', flex: '1' }}>
                    <input
                      type="text"
                      placeholder="Search or add email"
                      onClick={() => handleDropdownClick('dropdown2')}
                      onChange={(e) => setSearchQuery2(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          handleEmailChange(e.target.value, 'dropdown2');
                          setSearchQuery2('');
                        }
                      }}
                      value={searchQuery2}
                      style={{ flex: '1', minWidth: '150px', border: 'none', paddingLeft: '10px', fontSize: '16px', outline: 'none' }}
                      ref={inputRef2}
                    />
                    {searchQuery2 && ![...managers, ...recruiters].some((item) => item.email.toLowerCase().includes(searchQuery2.toLowerCase())) && (
                      <button
                        onClick={handleAddEmailWithValidations}
                        style={{
                          position: 'absolute',
                          right: '10px',
                          backgroundColor: '#007bff',
                          color: 'white',
                          border: 'none',
                          padding: '5px 10px',
                          borderRadius: '4px',
                        }}
                      >
                        Add
                      </button>
                    )}
                  </div>
                </div>
                {searchQuery2 && (
                  <div className="dropdown-menu" style={{ border: '1px solid #ccc', borderRadius: '4px', boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)', marginTop: '5px', width: "auto" }}>
                    {[...new Set([
                      ...managers.map(item => item.email.toLowerCase()),
                      ...recruiters.map(item => item.email.toLowerCase())
                    ])] // Removing duplicates
                      .filter(email => email.includes(searchQuery2.toLowerCase()) && email !== loggedInEmail)
                      .map((email, index) => (
                        <label key={index} className="dropdown-item" style={{ display: 'flex', padding: '8px', fontWeight: "normal", cursor: 'pointer' }}>
                          <input
                            type="checkbox"
                            checked={selectedEmails2?.includes(email)}
                            onChange={() => handleEmailChange(email, 'dropdown2')}
                            style={{ marginRight: '8px' }}
                          />
                          {email}
                        </label>
                      ))
                    }
                  </div>
                )}
              </div>
            </div>
            <div>
              <label style={{ display: 'block', marginBottom: '4px', fontSize: '16px', textAlign: "left" }}>Description</label>
              <textarea
                placeholder="Enter your text here"
                rows="2"
                cols="60"
                style={{
                  flex: '1',
                  width: window.innerWidth <= 542 ? '100%' : '100%',
                  border: '1px solid lightgray',
                  paddingLeft: '10px',
                  fontSize: '16px',
                  resize: 'none', // Prevent manual resizing
                  overflow: 'hidden', // Prevent scrollbar appearance
                }}
                value={description}
                onChange={handleDescriptionChange}
                onInput={(e) => {
                  e.target.style.height = 'auto'; // Reset height to auto
                  e.target.style.height = `${e.target.scrollHeight}px`; // Adjust height based on scrollHeight
                }}
              >
              </textarea>

            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
              <div style={{ width: '48%' }}>
                <label style={{ display: 'block', marginBottom: '4px', fontSize: '16px', textAlign: "left" }}>Start Date*</label>
                <input
                  type="date"
                  value={startDate}
                  onChange={handleStartDateChange}
                  style={{ width: '100%', height: "40px", borderRadius: "4px", border: "1px solid #ccc", paddingLeft: "10px", fontSize: '16px' }}
                />
              </div>
              <div style={{ width: '48%' }}>
                <label style={{ display: 'block', marginBottom: '4px', fontSize: '16px', textAlign: "left" }}>End Date*</label>
                <input
                  type="date"
                  value={endDate}
                  onChange={handleEndDateChange}
                  style={{ width: '100%', height: "40px", borderRadius: "4px", border: "1px solid #ccc", paddingLeft: "10px", fontSize: '16px' }}
                />
                {error && <p style={{ color: 'red' }}>{error}</p>}
              </div>
            </div>
            <div style={{ marginBottom: '5px' }}>
              <label style={{ display: 'block', marginBottom: '4px', fontSize: '16px', textAlign: "left" }}>Time Zone</label>
              <select
                value={selectedTimeZone}
                onChange={handleTimeZoneChange}
                style={{ width: '100%', height: "40px", borderRadius: "4px", border: "1px solid #ccc", paddingLeft: "10px", fontSize: '16px' }}
              >
                <option value="">Select a time zone</option>
                <option value="Asia/Kolkata">Asia/Kolkata</option>
                <option value="America/New_York">America/New_York</option>
                <option value="Europe/London">Europe/London</option>
                <option value="Australia/Sydney">Australia/Sydney</option>
                <option value="Asia/Tokyo">Asia/Tokyo</option>
              </select>
            </div>
            {/* <div style={{ marginBottom: '5px' }}>
              <label style={{ display: 'block', marginBottom: '4px', fontSize: '16px', textAlign: "left" }}>Attachement</label>
              <input type="file" multiple onChange={handleFileChange}
                style={{ width: '100%', padding: "4px", borderRadius: "4px", border: "1px solid #ccc", paddingLeft: "10px", fontSize: '16px', cursor: "pointer" }}
              />
            </div> */}
              <div style={{ width: "100%",marginBottom: '5px' }}>
                    <div style={{ width: "100%", maxHeight: "160px", overflowY: "auto" }} className="JS">
                            <label style={{ display: 'block', marginBottom: '4px', fontSize: '16px', textAlign: "left" ,fontWeight:"700"}}>Attachement :       {files.length > 0 && (
                      <span style={{ fontSize: "14px", color: "#333" }}>
                        {files.length} file{files.length > 1 ? "s" : ""} selected
                      </span>
                    )} </label>
                       
                              {/* <input
                                type="file"
                                name="jd_pdf"
                                id="jd_pdf"
                                multiple
                                accept=".pdf,.doc,.docx"
                                onChange={handleFileChange}
                                placeholder="select file"
                                
                               
                              /> */}
                              <input type="file" multiple onChange={handleFileChange}        accept=".pdf,.doc,.docx"
                style={{ width: '100%', padding: "4px", borderRadius: "4px", border: "1px solid #ccc", paddingLeft: "10px", fontSize: '13px', cursor: "pointer" }}
              />
                              {files.length > 0 && (
                                <ul style={{
                                  textAlign: "left",
                                  margin: 0,
                                  listStyle: "none",
                                  padding: 0,
                                  overflowY: "auto",
                                  maxHeight: "100px",
                                }}>
                                  {files.map((file, index) => (
                                    <li key={index} style={{
                                      display: "flex",
                                      alignItems: "center",
                                      gap: "8px",
                                      marginBottom: "4px",
                                    }}>
                                      <span>{file.name}</span>
                                      <button
                                        type="button"
                                        onClick={() => handleRemoveFile(index)}
                                        style={{
                                          background: "transparent",
                                          border: "none",
                                          color: "red",
                                          fontWeight: "bold",
                                          cursor: "pointer",
                                          fontSize: "14px",
                                        }}
                                      >
                                        ❌
                                      </button>
                                    </li>
                                  ))}
                                </ul>
                              )}
            
                            </div>
            
              </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '20px' }}>
              <div style={{ width: '100%' }}>
                <label style={{ display: 'block', marginBottom: '4px', fontSize: '16px', textAlign: "left" }}>Start Time*</label>
                <input
                  type="time"
                  list="startTimeOptions"
                  value={startTime}
                  onChange={(e) => setStartTime(e.target.value)}
                  style={{ width: '90%', height: '40px', borderRadius: '4px', border: '1px solid #ccc', paddingLeft: '10px', fontSize: '16px' }}
                />
                <datalist id="startTimeOptions">
                  {timeOptions.map(option => (
                    <option key={option.value} value={option.value}></option>
                  ))}
                </datalist>
              </div>
              <div style={{ width: '100%' }}>
                <label style={{ display: 'block', marginBottom: '4px', fontSize: '16px', textAlign: "left" }}>End Time*</label>
                <input
                  type="time"
                  list="endTimeOptions"
                  value={endTime}
                  onChange={(e) => setEndTime(e.target.value)}
                  style={{ width: '100%', height: '40px', borderRadius: '4px', border: '1px solid #ccc', paddingLeft: '10px', fontSize: '16px' }}
                />
                <datalist id="endTimeOptions">
                  {timeOptions.map(option => (
                    <option key={option.value} value={option.value}></option>
                  ))}
                </datalist>
              </div>
            </div>
            <div className="btns" style={{
      position:"sticky",
  bottom: "0",
  backgroundColor: "#fff",
   padding:"0px",
  zIndex: "100",
  display: "flex",
  gap: "10px",
  justifyContent: "flex-end"
            }} >
              <button
                type="button"
                onClick={handleModalClose}
                style={{ backgroundColor: "#e81123", color: "white", border: "none", borderRadius: "4px", padding: '0px 16px', fontSize: '16px', cursor: 'pointer', height: "30px" }}
              >
                Close
              </button>
              <button
                type="submit"
                style={{ backgroundColor: "#32406D", color: "white", border: "none", borderRadius: "4px", padding: '0px 16px', fontSize: '16px', cursor: 'pointer', position: 'relative', height: "30px", width: "80px" }}
              >
                {waitForSubmission1 ? "" : "Save"}
                <ThreeDots
                  wrapperClass="ovalSpinner"
                  wrapperStyle={{
                    position: "absolute",
                    right: "-5px",
                    transform: "translate(-50%, -50%)",
                  }}
                  visible={waitForSubmission1}
                  height="45"
                  width="45"
                  color="white"
                  ariaLabel="oval-loading"
                />
              </button>
            </div>
          </div>
        </form>
      </Modal>
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        style={{
          overlay: {
            backgroundColor: 'rgba(0, 0, 0, 0.75)',
            zIndex: 9999,
          },
          content: {
            color: 'lightsteelblue',
            top: '50%',
            left: '50%',
            right: 'auto',
            bottom: 'auto',
            marginRight: '-50%',
            transform: 'translate(-50%, -50%)',
            width: "350px",
            height: "150px",

          }
        }}>
        <div style={{ textAlign: 'center', marginTop: "10px" }}>
          <p style={{ color: "#000" }}>{modalMessage}</p>
          {/* <p style={{color:"green"}}>dfghsuidtuwefytyveiethetuekute</p> */}
        </div>
        <div style={{ display: 'flex', justifyContent: 'center', gap: '10px', width: '100%', marginTop: "30px" }}>
          <button
            onClick={() => {
              setIsModalOpen(false);
              if (responseSuccess) {
                setShowCalendar(true);
              }
            }}
            style={{
              color: "white",
              backgroundColor: "green",
              border: "none",
              width: "50px",
              height: "25px",
              borderRadius: "5px"
            }}
          >
            Ok
          </button>
          <button
            onClick={() => setIsModalOpen(false)}
            style={{
              color: "white",
              backgroundColor: "red",
              border: "none",
              width: "50px",
              height: "25px",
              borderRadius: "5px"
            }}
          >
            Close
          </button>
        </div>
      </Modal>
    </div>
  )

}

export default ScheduleMeet;