import React, { useEffect, useRef, useState } from "react";
import LeftNav from "../../Components/LeftNav";
import "../../Components/leftnav.css";
import TitleBar from "../../Components/TitleBar";
import "../../Components/titlenav.css";
import "../JobListing/editjobposting.css";
import { toast } from "react-toastify";
import { useLocation, useNavigate } from "react-router-dom";
import { FaArrowLeft } from "react-icons/fa";
import { useDispatch, useSelector } from "react-redux";
import Multiselect from "multiselect-react-dropdown";
import { getAllJobs, getDashboardData } from "../utilities";
import { ThreeDots } from "react-loader-spinner";

function EditJobPosting() {
  const dispatch = useDispatch();
  const option_ref = useRef();
  const openRef = useRef();
  const { activeUsers } = useSelector((state) => state.userSliceReducer);
  const [waitForSubmission, setwaitForSubmission] = useState(false);
  // console.log(activeUsers)
  const [showRecruiters, setShowRecruiters] = useState(1);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const location = useLocation();
  const loc = location.state.item;
  // console.log("loc", loc);
  const navigate = useNavigate();
  const jd_ref = useRef(null);
  const [show, setShow] = useState(true);
  const [recruiters, setRecruiters] = useState([]);
  const [budget_min_type, budget_min_value] = loc.budget_min.split(" ");
  const [budget_max_type, budget_max_value] = loc.budget_max.split(" ");
  const initialState = {
    client: loc.client,
    role: loc.role,
    skills: loc.skills,
    location: loc["location"],
    Job_Type: loc.job_type,
    mode: loc.mode,
    Job_Type_details: loc.contract_in_months,
    Custom_Job_Type: loc.custom_job_type,
    experience_min: loc.experience_min,
    experience_max: loc.experience_max,
    no_of_positions: loc.no_of_positions,
    budget_min_type,
    budget_min_value,
    budget_max_type,
    budget_max_value,
    // budget_min: loc.budget_min.split(" ")[1],
    // currency_type_min: loc.budget_min.split(" ")[0],
    // budget_max: loc.budget_max.split(" ")[1],
    // currency_type_max: loc.budget_max.split(" ")[0],
    shift_timings: loc.shift_timings,
    notice_period: loc.notice_period,
    job_status: loc.job_status,
    recruiter: loc.recruiter.split(", "),
    detailed_jd: loc.detailed_jd,
    jd_pdf: null,
  };
  const [formData, setFormData] = useState(initialState);
  // New files selected
  const [fileType, setFileType] = useState(null); // 'single' or 'multiple'
  const [selectedFiles, setSelectedFiles] = useState([]); // Holds newly selected files
  const [existingFiles, setExistingFiles] = useState([]); // Holds files fetched from backend

  const [removedFileIds, setRemovedFileIds] = useState([]);

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    setSelectedFiles(files); // Store new files
  };

  const handleRemoveFile = (fileId) => {
    setExistingFiles((prev) => prev.filter((file) => file.id !== fileId));
    setRemovedFileIds((prev) => [...prev, fileId]); // Track removed files
  };
  const handleRemoveFilenew = (indexToRemove) => {
    const updatedFiles = selectedFiles.filter((_, index) => index !== indexToRemove);
    setSelectedFiles(updatedFiles);

  };
  const fileToBase64 = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result.split(",")[1]);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  useEffect(() => {
    console.log("edint formData", formData);
  }, [formData]);




  useEffect(() => {
    const handleClick = (e) => {
      console.log("found click");
      if (option_ref?.current?.contains(e.target)) {
        setShowRecruiters(2);
        return;
      }
      if (showRecruiters === 2) {
        if (!option_ref?.current?.contains(e.target)) {
          console.log("matched");
          if (formData.recruiter.length === 0) {
            setShowRecruiters(1);
          } else {
            setShowRecruiters(3);
          }
        } else {
          console.log("not matched");
        }
      }
      // else{
      //     if(option_ref.current.contains(e.target)){
      //         setShowRecruiters(2)
      //     }
      // }
    };
    window.addEventListener("click", handleClick);
    return () => {
      window.removeEventListener("click", handleClick);
    };
  });

  const { recruiters: rdxRecruiters, managers } = useSelector(
    (state) => state.userSliceReducer,
  );

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        let arr = [];
        for (const item of rdxRecruiters) {
          if (item["is_active"] && item["is_verified"]) {
            arr.push(item["name"]);
          }
        }
        setRecruiters([...arr]);
      } catch (err) {
        console.error("Error fetching users:", err); // Log any errors
      }
    };
    fetchUsers(); // Call the fetch function inside useEffect
  }, [rdxRecruiters]);

  const [marginTopForDetailJD, setmarginTopForDetailJD] = useState("0");

  useEffect(() => {
    const sourceElement = document.getElementsByClassName("searchWrapper")[0];
    const targetElement = document.getElementById("detailedJdInput");

    if (sourceElement && targetElement) {
      const updateMarginTop = () => {
        const computedHeight = window.getComputedStyle(sourceElement).height;
        setmarginTopForDetailJD(computedHeight);
      };

      updateMarginTop();

      const resizeObserver = new ResizeObserver(() => {
        updateMarginTop();
      });

      resizeObserver.observe(sourceElement);

      return () => {
        resizeObserver.unobserve(sourceElement);
      };
    }
  }, []);

  useEffect(() => {
    const targetElement = document.getElementById("targetElement");
    if (targetElement) {
      targetElement.style.marginTop = marginTopForDetailJD;
    }
  }, [marginTopForDetailJD]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    console.log(name);
    console.log(value);
    if (e.target.name != "recruiter")
      setFormData({ ...formData, [name]: value });
    else {
      let recruiter_data = [];
      recruiter_data.push(e.target.value);
      // setFormData({...formData,recruiter:recruiter_data})
    }
    if (name === "no_of_positions" && value < 0) {
      return; // Ignore negative values
    }
  };





  const notify = () => toast.success("submitted successfully");
  const [filestype, setFilestype] = useState(null)
  const fetchJobData = async (jobId) => {
    try {
      const response = await fetch(`http://192.168.0.47:5002/get_job_data/${jobId}`);
      const data = await response.json();
      setFilestype(data.type);
      if (data.status === "success") {
        // Normalize files into array
        const normalizedFiles = Array.isArray(data.files) ? data.files : [data.files];
        console.log(normalizedFiles, "eeeeeeee")
        setExistingFiles(normalizedFiles);

        setFormData((prev) => ({
          ...prev,
          jd_pdf: normalizedFiles
        }));
      } else {
        toast.error("Failed to fetch job data.");
      }
    } catch (err) {
      toast.error("An error occurred while fetching job data.");
    }

  };
  // console.log(existingFiles,"33333333333333333333333333333333")

  useEffect(() => {
    fetchJobData(loc.id); // Use the job id from location or props
  }, []);


  // console.log("newfiles",newFiles.length )
  // console.log("oldfiles",oldFiles.length)

  const handleSubmit = async (e) => {
    if (!waitForSubmission) {
      setwaitForSubmission(true);
      e.preventDefault();
      // const isFormDataSame = JSON.stringify(formData) === JSON.stringify(initialState);
      // const isFilesSame = selectedFiles.length === 0 && existingFiles.length === loc.jd_files_length; // Assuming you have a length of old files or use old file list.

      // if (isFormDataSame && isFilesSame) {
      //   toast.warn("No changes detected. Please make edits before submitting.");
      //   setwaitForSubmission(false);
      //   return;
      // }
      const noNewFilesSelected = selectedFiles.length === 0;
      const noExistingFiles = existingFiles.length === 0;
      const formUnchanged = JSON.stringify(formData) === JSON.stringify(initialState);
      if (formUnchanged && noNewFilesSelected && noExistingFiles) {
        toast.warn("No changes detected. Please make edits before submitting.");
        setwaitForSubmission(false);
        return;
      }
      if (noNewFilesSelected && noExistingFiles) {
        toast.warn("Please upload at least one Detailed JD file (pdf/doc).");
        setwaitForSubmission(false);
        return;
      }
      if (
        parseFloat(formData.experience_min) >
        parseFloat(formData.experience_max)
      ) {
        toast.warn("Minimum experience must be less than maximum experience");
        setwaitForSubmission(false);
        return;
      }
      if (!formData.no_of_positions) {
        toast.warn("position would be mandatory!");
        setwaitForSubmission(false);
        return;
      }
      if (
        parseFloat(formData.budget_min_value) >
        parseFloat(formData.budget_max_value)
      ) {
        toast.warn("Minimum Budget must be less than maximum Budget");
        setwaitForSubmission(false);
        return;
      }
      let jd_pdf = null;
      let pdfs = null;
      let existing_file_ids = null;
      let fileextension = null;
      const newFiles = selectedFiles;
      const oldFiles = existingFiles;

      const newFilesExist = newFiles.length > 0;
      const oldFilesExist = oldFiles.length > 0;

      // Map existing file IDs
      const oldFileIds = oldFiles.map((file) => file.id);
      // console.log("filestype",filestype);
      if (filestype == "single") {
        // Case 2: No old files ever existed, single new file
        // jd_pdf = await fileToBase64(newFiles[0]);
        let existingFilessin = existingFiles || []; // from backend or form state
        let newFiles = [];

        if (selectedFiles.length === 1) {
          jd_pdf = await fileToBase64(selectedFiles[0]);
          fileextension = `${selectedFiles[0].name.split('.').pop()}`;
        } else {
          newFiles = await Promise.all(
            selectedFiles.map(async (file) => ({
              file_data: await fileToBase64(file),
              extension: file.name.split(".").pop(),
              filename: file.name
            }))
          );
        }
        pdfs = [...existingFilessin, ...newFiles];
      } else if (filestype == "multiple") {
        // Case 1 or 4: old + new, or old removed and new added
        pdfs = await Promise.all(
          newFiles.map(async (file) => ({
            filename: file.name,
            file_data: await fileToBase64(file),
            extension: `.${file.name.split('.').pop()}`
          }))
        );

        existing_file_ids = oldFileIds.length === 0 ? null : oldFileIds;
      } else if (oldFilesExist && !newFilesExist) {
        // Case 3: only old files remain
        existing_file_ids = oldFileIds;
      }


      const body_data = {
        user_id: localStorage.getItem("user_id"),
        client: formData.client,
        experience_min: formData.experience_min,
        experience_max: formData.experience_max,
        currency_type_min: formData.currency_type_min,
        currency_type_max: formData.currency_type_max,
        budget_min: `${formData.budget_min_type} ${formData.budget_min_value}`,
        budget_max: `${formData.budget_max_type} ${formData.budget_max_value}`,
        location: formData.location,
        shift_timings: formData.shift_timings,
        notice_period: formData.notice_period,
        role: formData.role,
        detailed_jd: formData.detailed_jd,
        jd_pdf: jd_pdf,
        pdfs: pdfs || [],
        file_type: fileextension,
        existing_file_ids: existing_file_ids || [],
        // files: updatedFiles,
        mode: formData.mode,
        job_status: formData.job_status,
        Job_Type: formData.Job_Type,
        Job_Type_details: formData.Job_Type_details,
        Custom_Job_Type: formData.Custom_Job_Type,
        skills: formData.skills,
        recruiter: formData.recruiter,
        no_of_positions: formData.no_of_positions
      };

      const job_id = loc.id;
      console.log("body_data", body_data);

      try {
        const response = await fetch(
          `http://192.168.0.47:5002/edit_job_post/${job_id}`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(body_data),
          },
        );

        const data = await response.json();
        console.log("data", data)
        if (data.status === "success") {
          // console.log("response data", data);
          // setFormData(initialState);

          getAllJobs().then(() => {
            toast.success(data.message);
            navigate("/JobListing");
            getDashboardData();
            fetch('http://192.168.0.47:5002/send_edit_notifications', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                job_post_id: data.job_post_id,
                old_recruiter_usernames: data.old_recruiter_usernames,
                new_recruiter_usernames: data.new_recruiter_usernames
              })
            });
          });
        } else {
          console.log(response.statusText);
          toast.error(data.message);
          setwaitForSubmission(false);
        }
      } catch (err) {
        console.log("handle error", err);
        setwaitForSubmission(false);
        toast.error("An Error Occurred Please Try Again Later");
      }
    }
  };
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);

  useEffect(() => {
    // console.log(showRecruiters);
    console.log("1");
    if (
      recruiters.length > 1 &&
      !recruiters.includes("Select All") &&
      formData.recruiter.length !== recruiters.length
    ) {
      console.log("3");
      setRecruiters((prev) => ["Select All", ...prev]);
    }
  }, [recruiters]);

  const handleChangeRecruiter = (selectedList) => {
    let selectAll = false;
    if (
      recruiters.includes("Select All") &&
      selectedList.length === recruiters.length - 1
    ) {
      selectAll = true;
    }
    selectedList.map((item) => {
      if (item.name === "Select All") {
        selectAll = true;
      }
    });

    let selectedRecruiters = [];
    if (
      selectAll ||
      selectedList.map((item) => item.name).includes("Select All")
    ) {
      selectedRecruiters.push(...recruiters.slice(1));
      setRecruiters((prev) => [...prev.slice(1)]);
    } else {
      selectedRecruiters.push(...selectedList.map((item) => item.name));
      if (!recruiters.includes("Select All")) {
        setRecruiters((prev) => ["Select All", ...prev]);
      }
    }
    // console.log("udpaetd recruiters",  [...recruiters.slice(1)])
    console.log("recruiters", recruiters);
    console.log("selectedList", selectedList);
    console.log("selectedList len", selectedList.length);
    console.log("selectedRecruiters", selectedRecruiters);
    setFormData({
      ...formData,
      recruiter: [...selectedRecruiters],
    });
    // setShowRecruiters(selectedRecruiters.length > 0 ? 3 : 1);
  };

  useEffect(() => {
    localStorage.setItem("path", location.state.path);
  }, []);
  return (
    <div className={`wrapper ${sidebarOpen ? "active" : ""}`}>
      <LeftNav />
      <div className="section">
        <TitleBar />
        <div
          style={{}}
          className="editjobpost"
        >
          <button
            className="back-button"
            onClick={() => navigate("/JobListing")}
            style={{ marginTop: "4px" }}
          >
            <FaArrowLeft />
          </button>
          <h3 className="headingtwo3" style={{ paddingLeft: "37%" }}>
            Edit Job Posting
          </h3>
        </div>
        <div className="Container">
          <form className="forms" encType="multipart/form-data">
            <div className="group">
              <div className="JS">
                <label htmlFor="client">
                  <span className="required-field">*</span>Client:
                </label>
                <input
                  type="text"
                  id="client"
                  name="client"
                  value={formData.client}
                  onChange={handleChange}
                  placeholder="Select Existing Client or Enter New Client"
                  className="form-control"
                  list="clients"
                  autoComplete="off"
                />
                <datalist id="clients">{/* Options */}</datalist>
                <small></small>
              </div>

              {/* Role */}
              <div className="JS">
                <label htmlFor="role">
                  <span className="required-field">*</span>Role:
                </label>
                <input
                  type="text"
                  id="role"
                  name="role"
                  value={formData.role}
                  onChange={handleChange}
                  className="form-control"
                />
                <small></small>
              </div>

              {/* Skills */}
              <div className="JS">
                <label htmlFor="skills">
                  <span className="required-field">*</span>Skills:
                </label>
                <input
                  type="text"
                  id="skills"
                  name="skills"
                  value={formData.skills}
                  onChange={handleChange}
                  className="form-control"
                />
                <small></small>
              </div>
              {/* No Of Positions */}
              <div className="JS posi">
                <label htmlFor="no_of_positions">
                  <span className="required-field">*</span>No Of Positions:
                </label>
                <input
                  type="text"
                  id="no_of_positions"
                  name="no_of_positions"
                  value={formData.no_of_positions}

                  onChange={(e) => {
                    const value = e.target.value;
                    if (value >= 0) {
                      handleChange(e); // Call your existing handler if value is valid
                    }
                  }}
                  className="form-control"
                  min="0"
                  required
                />
                <small></small>
              </div>
              {/* Location */}
              <div className="JS" style={{ marginTop: formData.Job_Type === "Contract" || formData.Job_Type === "Custom" ? "-37px" : "0px" }}>
                <label htmlFor="location">
                  <span className="required-field">*</span>Location:
                </label>
                <input
                  type="text"
                  id="location"
                  name="location"
                  value={formData.location}
                  onChange={handleChange}
                  className="form-control"
                />
                <small></small>
              </div>

              <div className="JS">
                <label htmlFor="Job_Type">
                  <span className="required-field">*</span>Job Type:
                </label>
                <select
                  id="Job_Type"
                  name="Job_Type"
                  value={formData.Job_Type}
                  onChange={handleChange}
                  className="form-select"
                >
                  <option value="" disabled>
                    Select Job Type
                  </option>
                  <option value="Permanent with the client">
                    Permanent with the client
                  </option>
                  <option value="Permanent with Makonis">
                    Permanent with Makonis
                  </option>
                  <option value="Contract">Contract</option>
                  <option value="Custom">Custom</option>
                </select>
                <small></small>
                {formData.Job_Type === "Contract" && (
                  <span style={{ display: "flex", marginTop: "10px", justifyContent: "space-around" }}>
                    <label
                      htmlFor="Job_Type_details"
                      style={{ fontSize: "12px", paddingTop: "5px" }}
                    >
                      <span className="required-field">*</span>Contract in
                      months:
                    </label>
                    <input
                      type="text"
                      id="Job_Type_details"
                      name="Job_Type_details"
                      value={formData.Job_Type_details}
                      onChange={handleChange}
                      className="form-control"
                      style={{
                        width: "60%",
                        paddingLeft: "5px",
                        marginLeft: "7px",
                      }}
                    />
                    <small></small>
                  </span>
                )}
                {formData.Job_Type === "Custom" && (
                  <span style={{ display: "flex", marginTop: "10px", justifyContent: "space-around" }}>
                    <label htmlFor="Custom_Job_Type" style={{ fontSize: "12px", paddingTop: "5px" }}>
                      <span className="required-field">*</span>Custom Job Type:
                    </label>
                    <input
                      type="text"
                      id="Custom_Job_Type"
                      name="Custom_Job_Type"
                      value={formData.Custom_Job_Type}
                      onChange={handleChange}
                      className="form-control"

                      style={{
                        width: "70%",
                        // paddingLeft: "0px",
                        // marginLeft: "0px",
                      }}
                      placeholder="Enter custom job type"
                    />
                  </span>
                )}
              </div>

              {/* Mode of Work */}
              <div
                className="JS"

              >
                <label htmlFor="mode">
                  <span className="required-field">*</span>Mode of Work:
                </label>
                <select
                  id="mode"
                  name="mode"
                  value={formData.mode}
                  onChange={handleChange}
                  className="form-select"
                >
                  <option value="" disabled>
                    Select Mode of Work
                  </option>
                  <option value="Hybrid">Hybrid</option>
                  <option value="WFO">WFO</option>
                  <option value="WFH">WFH</option>
                </select>
                <small></small>
              </div>

              {/* Experience Min */}
              <div className="JS">
                <label htmlFor="experience_min">
                  <span className="required-field">*</span>Minimum Experience:
                </label>
                <input
                  type="number"
                  id="experience_min"
                  name="experience_min"
                  value={formData.experience_min}
                  onChange={handleChange}
                  className="form-control"
                  placeholder="Numerics"
                  min="0"
                  step="0.1"
                />
                <small></small>
              </div>

              {/* Experience Max */}
              <div className="JS">
                <label htmlFor="experience_max">
                  <span className="required-field">*</span>Maximum Experience:
                </label>
                <input
                  type="number"
                  id="experience_max"
                  name="experience_max"
                  value={formData.experience_max}
                  onChange={handleChange}
                  className="form-control"
                  placeholder="Numerics"
                  min="0"
                  step="0.1"
                />
                <small></small>
              </div>

              {/* Budget Min */}
              <div className="JS">
                <label htmlFor="budget_min">
                  <span className="required-field">*</span>Minimum Budget:
                </label>
                <div className="currency-input">
                  <select
                    id="currency_type_min"
                    name="budget_min_type"
                    value={formData.budget_min_type}
                    onChange={handleChange}
                    required
                    className="small-select" // Add a class for styling
                  >
                    <option value="INR">INR (LPA)</option>
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="CAD">CAD</option>
                  </select>
                  <input
                    type="number"
                    id="budget_min"
                    name="budget_min_value"
                    value={formData.budget_min_value}
                    onChange={handleChange}
                    className="large-input" // Add a class for styling
                    placeholder="Numerics"
                    min="0"
                    step="0.1"
                  />
                </div>
              </div>

              {/* Budget Max */}
              <div className="JS">
                <label htmlFor="budget_max">
                  <span className="required-field">*</span>Maximum Budget:
                </label>
                <div className="currency-input">
                  <select
                    id="currency_type_max"
                    name="budget_max_type"
                    value={formData.budget_max_type}
                    onChange={handleChange}
                    required
                    className="small-select" // Add a class for styling
                  >
                    <option value="INR">INR (LPA)</option>
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="CAD">CAD</option>
                  </select>
                  <input
                    type="number"
                    id="budget_max"
                    name="budget_max_value"
                    value={formData.budget_max_value}
                    onChange={handleChange}
                    className="large-input" // Add a class for styling
                    placeholder="Numerics"
                    min="0"
                    step="0.1"
                  />
                </div>
                <small></small>
              </div>

              {/* Shift Timings */}
              <div className="JS">
                <label htmlFor="shift_timings">
                  <span className="required-field">*</span>Shift Timings:
                </label>
                <select
                  id="shift_timings"
                  name="shift_timings"
                  value={formData.shift_timings}
                  onChange={handleChange}
                  className="form-select"
                >
                  <option value="" disabled>
                    Select Shift Timings
                  </option>
                  <option value="General">General</option>
                  <option value="Rotational">Rotational</option>
                </select>
                <small></small>
              </div>

              {/* Notice Period */}

              <div className="JS">
                <label htmlFor="notice_period">
                  <span className="required-field">*</span>Notice Period:
                </label>
                <input
                  type="number"
                  id="notice_period"
                  name="notice_period"
                  value={formData.notice_period}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (/^\d*$/.test(value)) {
                      handleChange(e);
                    }
                  }}
                  className="form-control"
                />
                <small></small>
              </div>

              {/* Job Status */}
              <div className="JS"  >
                <label htmlFor="job_status">
                  <span className="required-field">*</span>Job Status:
                </label>
                <select
                  id="job_status"
                  name="job_status"
                  value={formData.job_status}
                  onChange={handleChange}
                  className="form-select"
                >
                  <option value="" disabled>
                    Select Job Status
                  </option>
                  <option value="Active">Active</option>
                  <option value="Hold">Hold</option>
                  <option value="Close">Close</option>
                </select>
                <small></small>
              </div>

              {/* Recruiter */}
              {/* Detailed Job Description */}
              <div style={{ display: "flex", gap: "20px", width: "100%" }}>
                <div className="JS" style={{ width: "50%", maxHeight: "160px", overflowY: "auto" }} >
                  <label htmlFor="jd_pdf">
                    <span style={{ color: "red" }}>*</span> Detailed JD:

                  </label>

                  {/* File Upload Input */}
                  <input
                    type="file"
                    id="jd_pdf"
                    accept=".pdf,.doc,.docx"
                    multiple
                    onChange={handleFileChange}
                  />

                  {/* Newly Selected Files Preview */}

                  {(existingFiles.length > 0 || selectedFiles.length > 0) && (
                    <div
                      style={{
                        textAlign: "left",
                        maxHeight: "150px",
                        overflowY: "auto",
                        padding: "8px",
                        // border: "1px solid #ddd",
                        borderRadius: "5px",
                        backgroundColor: "#fafafa",
                        // marginTop: "10px",
                      }}
                    >
                      <ul style={{ listStyle: "none", padding: 0, margin: 0 }}>
                        {existingFiles.map((file, index) => (
                          <li
                            key={`existing-${index}`}
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                              marginBottom: "4px",
                            }}
                          >
                            <span style={{ color: "green", fontSize: "15px" }}>
                              {file.filename}
                            </span>
                            <button
                              type="button"
                              onClick={() => handleRemoveFile(file.id)}
                              style={{
                                background: "transparent",
                                border: "none",
                                color: "red",
                                fontWeight: "bold",
                                cursor: "pointer",
                                fontSize: "14px",
                              }}
                            >
                              ❌
                            </button>
                          </li>
                        ))}
                        {selectedFiles.map((file, index) => (
                          <li
                            key={`selected-${index}`}
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                              marginBottom: "4px",
                            }}
                          >
                            <span style={{ fontSize: "15px" }}>{file.name}</span>
                            <button
                              type="button"
                              onClick={() => handleRemoveFilenew(index)}
                              style={{
                                background: "transparent",
                                border: "none",
                                color: "red",
                                fontWeight: "bold",
                                cursor: "pointer",
                                fontSize: "14px",
                              }}
                            >
                              ❌
                            </button>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                </div>


                {/* Recruiter */}
                <div className="JS posit" style={{ width: "50%" }} id='recruiter'>
                  <label htmlFor="recruiter" id='recru' >
                    <span className="required-field" style={{ marginTop: "-30px" }}>*</span>Recruiter:
                  </label>
                  <div className="recruiter-selection">
                    <Multiselect
                      options={recruiters.map((item) => ({ name: item }))} // Convert recruiters array into format required by Multiselect
                      selectedValues={formData.recruiter.map((item) => ({
                        name: item,
                      }))} // Convert selected recruiters into format required by Multiselect
                      onSelect={handleChangeRecruiter}
                      onRemove={handleChangeRecruiter}
                      displayValue="name"
                      style={{ searchWrapper: { minHeight: "10px" } }} // Adjust width as needed
                      placeholder="Select recruiters"
                      className="custom-multiselect"
                    />
                  </div>
                </div>
              </div>

              {/* <div style={{height:'70px',width:'50%',backgroundColor:'blue'}}>
 
                        </div> */}
              <div
                className="JS"
                id="detailedJdInput"
                style={{ width: "50%", marginTop: "1px", paddingBottom: "20px" }}
              >
                <label
                  //  style={{backgroundColor:'blue'}}
                  htmlFor="detailed_jd" style={{ fontWeight: "400", fontSize: "13px", marginBottom: "5px" }}
                >
                  Detailed Job Description:
                </label>
                <textarea
                  style={{ width: "100%" }}
                  ref={jd_ref}
                  id="detailed_jd"
                  name="detailed_jd"
                  value={formData.detailed_jd}
                  onChange={handleChange}
                ></textarea>
                {/* <span style={{position: 'fixed', background: 'gray', height: '100px', width: '340px', transform: 'translate(10%, -10%)',overflowY:'auto'}}>
                                {formData.detailed_jd}
                            </span> */}
              </div>
            </div>
          </form>
        </div>
        {/* Submit Button */}
        <div className="buttons">
          <input
            onClick={handleSubmit}
            type="submit"
            value={waitForSubmission ? "" : "Submit"}
            id="submits"
            disabled={waitForSubmission}
          />
          <ThreeDots
            wrapperClass="ovalSpinner"
            wrapperStyle={{ position: "absolute", bottom: "10px" }}
            visible={waitForSubmission}
            height="45"
            width="45"
            color="white"
            ariaLabel="oval-loading"
          />
        </div>
      </div>
    </div>
  );
}
export default EditJobPosting;
