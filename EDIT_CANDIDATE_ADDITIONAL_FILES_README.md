# Edit Candidate - Additional Files Functionality

## Overview
I have successfully implemented the additional files input functionality for the Edit Candidate page, similar to the functionality used in Edit Job Post for the Detailed JD input. This allows users to upload, manage, and edit additional files for candidates.

## Features Implemented

### 1. File Upload Functionality
- **Multiple File Support**: Users can upload multiple files (.pdf, .doc, .docx)
- **File Type Validation**: Only accepts PDF and DOC/DOCX files
- **File Preview**: Shows selected files before submission
- **File Management**: Users can remove files before submitting

### 2. Existing Files Management
- **Fetch Existing Files**: Automatically loads previously uploaded files for the candidate
- **Display Existing Files**: Shows existing files with green text to distinguish from new files
- **Remove Existing Files**: Users can remove previously uploaded files
- **Track Removed Files**: Keeps track of removed file IDs for backend processing

### 3. File Processing Logic
- **Base64 Conversion**: Converts files to Base64 for API transmission
- **File Type Detection**: Automatically detects file extensions
- **Batch Processing**: Handles multiple files efficiently
- **Error Handling**: Proper error handling for file operations

### 4. UI/UX Improvements
- **Clean Interface**: Well-organized file upload section
- **Visual Feedback**: Different colors for existing vs new files
- **Responsive Design**: Works well on different screen sizes
- **Hover Effects**: Interactive buttons with hover states
- **File List Display**: Organized list view with remove buttons

## Technical Implementation

### State Management
```javascript
const [selectedFiles, setSelectedFiles] = useState([]); // New files
const [existingFiles, setExistingFiles] = useState([]); // Existing files from backend
const [removedFileIds, setRemovedFileIds] = useState([]); // Tracked removed files
const [filestype, setFilestype] = useState(null); // File type from backend
```

### File Handling Functions
```javascript
// Handle new file selection
const handleFileChange = (e) => {
  const files = Array.from(e.target.files);
  setSelectedFiles(files);
};

// Remove existing file
const handleRemoveFile = (fileId) => {
  setExistingFiles((prev) => prev.filter((file) => file.id !== fileId));
  setRemovedFileIds((prev) => [...prev, fileId]);
};

// Remove newly selected file
const handleRemoveFilenew = (indexToRemove) => {
  const updatedFiles = selectedFiles.filter((_, index) => index !== indexToRemove);
  setSelectedFiles(updatedFiles);
};
```

### Backend Integration
```javascript
// Fetch existing candidate files
const fetchCandidateData = async (candidateId) => {
  try {
    const response = await fetch(`http://************:5002/get_candidate_data/${candidateId}`);
    const data = await response.json();
    setFilestype(data.type);
    if (data.status === "success") {
      const normalizedFiles = Array.isArray(data.files) ? data.files : [data.files];
      setExistingFiles(normalizedFiles);
    }
  } catch (err) {
    console.log("Error fetching candidate data:", err);
  }
};
```

### API Data Structure
The additional files data is included in the API call:
```javascript
const edit_candidate_data = {
  ...formData,
  // Additional files data
  jd_pdf: jd_pdf,                    // Single file (if applicable)
  pdfs: pdfs || [],                  // Multiple files array
  file_type: fileextension,          // File extension
  existing_file_ids: existing_file_ids || [], // IDs of existing files to keep
  removed_file_ids: removedFileIds || [],     // IDs of files to remove
};
```

## File Processing Logic

### Single File Handling
- When only one file is selected, it's processed as `jd_pdf`
- File extension is extracted and stored separately
- Base64 conversion is applied

### Multiple Files Handling
- Multiple files are processed as an array in `pdfs`
- Each file includes: filename, file_data (Base64), extension
- Existing files are preserved unless explicitly removed

### File Type Management
- **Single**: Backend expects single file format
- **Multiple**: Backend expects array format
- **Default**: Falls back to multiple file handling

## CSS Styling

### Additional Files Container
```css
.additional-files-container {
  max-height: 150px;
  overflow-y: auto;
  padding: 8px;
  border-radius: 5px;
  background-color: #fafafa;
  border: 1px solid #e0e0e0;
}
```

### File List Items
```css
.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
  padding: 4px 8px;
  background-color: white;
  border-radius: 3px;
  border: 1px solid #ddd;
}
```

## Usage Instructions

1. **Upload Files**: Click "Choose Files" to select additional files
2. **Preview Files**: Selected files appear in the list below
3. **Remove Files**: Click the ❌ button next to any file to remove it
4. **Existing Files**: Previously uploaded files appear in green
5. **Submit**: Click "Update" to save changes with file updates

## File Management Features

- **Add Files**: Select multiple files at once
- **Remove New Files**: Remove newly selected files before submission
- **Remove Existing Files**: Remove previously uploaded files
- **File Validation**: Only PDF, DOC, and DOCX files are accepted
- **Visual Distinction**: Existing files (green) vs new files (black)

## Error Handling

- **File Type Validation**: Only accepts specified file types
- **API Error Handling**: Proper error messages for failed uploads
- **Network Error Handling**: Graceful handling of network issues
- **File Size Considerations**: Backend handles file size limitations

## Integration Notes

- **Backend Endpoint**: Uses `/get_candidate_data/{candidateId}` to fetch existing files
- **Update Endpoint**: Sends file data to `/edit_candidate/{candidateId}`
- **File Storage**: Files are stored as Base64 in the database
- **File Retrieval**: Existing files are fetched on component mount

## Browser Compatibility
- Works with all modern browsers
- File API support required
- Base64 encoding/decoding support

The additional files functionality is now fully integrated and provides a seamless experience for managing candidate documents, similar to the job posting file management system.
