# User Accounts Page - Peer Reviewer Toggle Fix

## Problem Description
When toggling the peer reviewer status on any page (e.g., page 2), the page would automatically jump back to page 1. This was causing a poor user experience as users would lose their current position in the pagination.

## Root Cause Analysis
The issue was caused by a chain reaction in the component's state management:

1. **Peer Reviewer Toggle** → Updates Redux store with new peer reviewer status
2. **Redux Store Update** → Triggers component re-render with updated `activeUsers` data
3. **Component Re-render** → Changes `filteredRows` state (derived from `activeUsers`)
4. **filteredRows Change** → Triggers useEffect with `[filteredRows, searchValue]` dependencies
5. **useEffect Execution** → Calls `fun()` function which calls `setId(1)` 
6. **setId(1)** → Resets pagination to page 1

## Solution Implemented

### 1. Added Tracking Mechanism
```javascript
// Ref to track if we're updating due to peer reviewer toggle
const isUpdatingPeerReviewer = useRef(false);
```

### 2. Updated handlePeerReviewerToggle Function
```javascript
const handlePeerReviewerToggle = async (user_id, currentStatus) => {
  try {
    // Set flag to indicate we're updating peer reviewer status
    isUpdatingPeerReviewer.current = true;
    
    // ... API call and Redux update logic ...
    
    // Reset flag after a short delay to allow React to process the update
    setTimeout(() => {
      isUpdatingPeerReviewer.current = false;
    }, 100);
    
  } catch (error) {
    // Reset flag on error as well
    isUpdatingPeerReviewer.current = false;
  }
};
```

### 3. Modified fun() Function Logic
```javascript
const fun = (data) => {
  const list = data.filter((it) => {
    return filteredRows.some((item) => item.id === it.id);
  });
  setBelowCount(list.length);
  
  // Only reset to page 1 if it's not a peer reviewer update and there's a search value
  if (!isUpdatingPeerReviewer.current && searchValue !== "") {
    setId(1);
  }
};
```

## Key Changes Made

### Before Fix:
- `fun()` function always called `setId(1)` regardless of the reason for data update
- Any Redux store update would reset pagination to page 1
- Poor user experience when toggling peer reviewer status

### After Fix:
- Added intelligent detection of peer reviewer updates using `useRef`
- Page reset only occurs when:
  - It's NOT a peer reviewer update AND
  - There's an actual search value (indicating user-initiated search/filter)
- Preserves current page position during peer reviewer toggles
- Maintains expected behavior for search and filter operations

## Behavior Matrix

| Action | Search Value | Page Reset | Reason |
|--------|-------------|------------|---------|
| Peer Reviewer Toggle | Empty | ❌ No | Not a search operation |
| Peer Reviewer Toggle | Has Value | ❌ No | Peer reviewer flag prevents reset |
| Search/Filter | Any | ✅ Yes | Expected behavior for new search |
| Apply Filters (OK button) | Any | ✅ Yes | Explicit `setId(1)` call |

## Technical Details

### State Management:
- Uses `useRef` instead of `useState` to avoid triggering additional re-renders
- Flag is set before API call and cleared after Redux update
- Timeout ensures React has processed the state update before clearing flag

### Error Handling:
- Flag is cleared in both success and error scenarios
- Prevents flag from getting stuck in `true` state

### Performance:
- Minimal overhead - only adds a boolean flag check
- No additional API calls or complex state management
- Preserves existing functionality for legitimate page resets

## Testing Scenarios

1. **✅ Toggle peer reviewer on page 2** → Stays on page 2
2. **✅ Toggle peer reviewer on page 3** → Stays on page 3  
3. **✅ Search for users** → Resets to page 1 (expected)
4. **✅ Apply filters** → Resets to page 1 (expected)
5. **✅ Clear search** → Maintains current page
6. **✅ Error during toggle** → Flag is properly cleared

## Benefits

1. **Improved UX**: Users maintain their position when toggling peer reviewer status
2. **Preserved Functionality**: Search and filter operations still reset to page 1 as expected
3. **Minimal Code Changes**: Solution is lightweight and doesn't affect other functionality
4. **Robust Error Handling**: Handles edge cases and error scenarios properly
5. **Future-Proof**: Pattern can be applied to other similar toggle operations

The fix ensures that pagination behavior is contextually appropriate - resetting only when users perform search/filter operations, not when making data updates like toggling peer reviewer status.
