import React, { useState, useEffect } from "react";
import LeftNav from "../../Components/LeftNav";
import "../../Components/leftnav.css";
import TitleBar from "../../Components/TitleBar";
import "../../Components/titlenav.css";
// import "./UpdateCandidate.css";
import "./EditCandidate.css";
import { getDashboardData } from "../utilities.js";

import { ThreeDots } from "react-loader-spinner";

import { useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
// import "../Views/UpdateCandidate.css";
import { useLocation, useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import Cookies from "universal-cookie";
import { FaArrowLeft } from "react-icons/fa";
const cookies = new Cookies();
function EditCandidate() {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const nameRegex = /^[A-Za-z\s]+$/;
  const last_working_date_ref = useRef(null)
  const USERTYPE = cookies.get("USERTYPE");
  const location = useLocation();
  const navigate = useNavigate();
   const [selectedFiles, setSelectedFiles] = useState([]); // Holds newly selected files
    const [existingFiles, setExistingFiles] = useState([]);
  // console.log(location.state);
  const editData = location.state.item;
  console.log("all datas", editData)
  const [current_ctc_type, current_ctc_value] = editData.current_ctc.split(" ");
  const [expected_ctc_type, expected_ctc_value] =
    editData.expected_ctc.split(" ");
  let s1 = editData.relevant_experience ? editData.relevant_experience : "0";
  const a1 = s1.split(/[ .]/);
  // console.log("a1", a1);
  const [relevant_experience_years, relevant_exp_months] = a1;
  const a2 = editData.experience.split(/[ .]/);
  // console.log("a2", a2);
  const [experience_years, exp_months] = a2;
  const [waitForSubmission, setwaitForSubmission] = useState(false);
  const { jobs } = useSelector((state) => state.jobSliceReducer);
  // console.log(jobs,"svsdfvsvsd")

  let filteredJobs;

  if (editData.recruiter) {
    filteredJobs = jobs.filter((job) => {
      const recruiters = job.recruiter?.split(",").map(r => r.trim().toLowerCase()) || [];
      return recruiters.includes(editData.recruiter.toLowerCase());
    });
  } else {
    // If recruiter is null, assume management view and return all jobs
    filteredJobs = jobs;
  }


// Normalize additional_files



  // console.log("filteredJobs", filteredJobs.length);

  const handleJobChange = (e) => {
    const selectedJobId = parseInt(e.target.value);
    const selectedJob = filteredJobs.find(job => job.id === selectedJobId);

    if (selectedJob) {
      setFormData((prev) => ({
        ...prev,
        job_id: selectedJobId,
        profile: selectedJob.role,
        client: selectedJob.client,
        // skills: selectedJob.skills,
        // you can auto-fill other fields too here if needed
      }));
    }
  };

  const [removedFileIds, setRemovedFileIds] = useState([]);
  // const [filestype, setFilestype] = useState(null);

useEffect(() => {
  if (editData?.additional_files) {
    try {
      const files =
        typeof editData.additional_files === "string"
          ? JSON.parse(editData.additional_files)
          : editData.additional_files;

      const normalizedFiles = Array.isArray(files) ? files : [files];
      console.log(normalizedFiles, "Normalized Existing Files");
      setExistingFiles(normalizedFiles);
    } catch (error) {
      console.error("Failed to parse additional_files:", error);
      setExistingFiles([]);
    }
  }
}, [editData]);

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    setSelectedFiles(files); // Store new files
  };

  const handleRemoveFile = (fileId) => {
    setExistingFiles((prev) => prev.filter((file) => file.resume_id !== fileId));
    setRemovedFileIds((prev) => [...prev, fileId]); // Track removed files
  };
  const handleRemoveFilenew = (indexToRemove) => {
    const updatedFiles = selectedFiles.filter((_, index) => index !== indexToRemove);
    setSelectedFiles(updatedFiles);

  };
  const fileToBase64 = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result.split(",")[1]);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };
  const initialState = {
    job_id: editData.job_id,
    name: editData.name,
    mobile: editData.mobile,
    email: editData.email,
    client: editData.client,
    profile: editData.profile,
    skills: editData.skills,
    current_company: editData.current_company,
    position: editData.position,
    reason_for_job_change:
      editData.reason_for_job_change !== null && editData.reason_for_job_change !== undefined
        ? editData.reason_for_job_change
        : "none",
    resume: null,
    current_job_location: editData.current_job_location,
    preferred_job_location: editData.preferred_job_location,
    experience_years,
    exp_months: parseInt(exp_months) ? exp_months : "0",
    relevant_experience_years,
    relevant_exp_months: parseInt(relevant_exp_months)
      ? relevant_exp_months
      : "0",
    current_ctc_type,
    current_ctc_value,
    expected_ctc_type,
    expected_ctc_value,
    qualifications: editData.qualifications,
    serving_notice_period:
      editData.serving_notice_period === null
        ? ""
        : editData.serving_notice_period,
    period_of_notice:
      editData.period_of_notice === null ? "" : editData.period_of_notice,
    last_working_date: editData?.last_working_date,
    buyout: editData.buyout,
    holding_offer: editData.holding_offer,
    total_offers: editData.total_offers === null ? "0" : editData.total_offers,
    highest_package:
      editData.highest_package_in_lpa === null
        ? "0"
        : editData.highest_package_in_lpa,
    linkedin: editData.linkedin === null ? "" : editData.linkedin,
    remarks: editData.remarks,
    // additional_files:editData.additional_files,
    // pdfs:editData.additional_files,
  }
  const [formData, setFormData] = useState(initialState)
  // console.log("edit formData", formData);

  const handleChange = (e) => {
    const { name, value, type, checked, files } = e.target;
    if (name === "resume" && files.length > 0) {
      const file = files[0];
      const reader = new FileReader();
      reader.onloadend = () => {
        setFormData((prevData) => ({
          ...prevData,
          resume: reader.result.split(",")[1],
        }));
      };
      reader.readAsDataURL(file);
    } else {
      const newValue = type === "checkbox" ? checked : value;
      setFormData({ ...formData, [name]: newValue });
    }
  };
  const exp_yrs = new Array(31).fill(0).map((_, idx) => idx); // Creates [0, 1, ..., 30]
  const exp_month = new Array(12).fill(0).map((_, idx) => idx); // Creates [0, 1, ..., 11]
  const selectStyle = {
    width: "70px", // Adjust as per your requirement
    height: "30px", // Adjust as per your requirement
    fontSize: "14px", // Optional for font size control
  };

  const notify = () => toast.success("Candidate edited successfully");
  const notify1 = () => toast.error("Resume is mandatory");
  const notify2 = () => toast.warn("Email format is incorrect");
  const isValidEmail = (email) => {
    return emailRegex.test(email);
  };

  const isValidName = (name) => {
    return nameRegex.test(name);
  };

  const isValidMobileNumber = (mobileNumber) => {
    return /^\d{10}$/.test(mobileNumber);
  };

  // Fetch existing candidate files
  // const fetchCandidateData = async (candidateId) => {
  //   try {
  //     const response = await fetch(`http://192.168.0.47:5002/get_candidate_data/${candidateId}`);
  //     const data = await response.json();
  //     setFilestype(data.type);
  //     if (data.status === "success") {
  //       // Normalize files into array
  //       const normalizedFiles = Array.isArray(data.files) ? data.files : [data.files];
  //       console.log(normalizedFiles, "candidate files")
  //       setExistingFiles(normalizedFiles);
  //     } else {
  //       console.log("No additional files found for this candidate.");
  //     }
  //   } catch (err) {
  //     console.log("Error fetching candidate data:", err);
  //   }
  // };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (waitForSubmission) return; // Prevent duplicate submissions

    setwaitForSubmission(true);

    // Utility function for error handling


    // if (JSON.stringify(formData) === JSON.stringify(initialState)) {
    //   toast.warn("No changes detected. Please make edits before submitting.");
    //   setwaitForSubmission(false);
    //   return;
    // }

    const showError = (message) => {
      toast.warn(message);
      setwaitForSubmission(false);
    };

    // Mandatory field validations
    if (!formData.qualifications) {
      showError("Qualifications are mandatory.");
      return;
    }
    if (
      !formData.relevant_experience_years ||
      formData.relevant_experience_years.trim() === ""

    ) {
      showError("Relevant experience is Required.");
      return;
    }
    if (
      parseFloat(formData.expected_ctc_value) <= parseFloat(formData.current_ctc_value)
    ) {
      showError("Expected CTC should be greater than Current CTC.");
      return;
    }
    if (
      !formData.experience_years ||
      formData.experience_years.trim() === ""

    ) {
      showError("Total experience is Required.");
      return;
    }

    if (
      !formData.expected_ctc_value ||
      formData.expected_ctc_value.trim() === "" ||
      !formData.expected_ctc_type ||
      formData.expected_ctc_type.trim() === ""
    ) {
      showError("Expected CTC is Required.");
      return;
    }

    if (
      !formData.current_ctc_value ||
      formData.current_ctc_value.trim() === "" ||
      !formData.current_ctc_type ||
      formData.current_ctc_type.trim() === ""
    ) {
      showError("Current CTC is Required.");
      return;
    }


    if (!isValidEmail(formData.email)) {
      showError("Please enter a valid email address.");
      return;
    }

    if (!isValidMobileNumber(formData.mobile)) {
      showError("Mobile number must contain exactly 10 digits.");
      return;
    }

    if (!isValidName(formData.name)) {
      showError("Candidate name should contain alphabetic characters only.");
      return;
    }

    if (
      parseFloat(formData.relevant_experience_years) >
      parseFloat(formData.experience_years) ||
      (parseFloat(formData.relevant_experience_years) ===
        parseFloat(formData.experience_years) &&
        parseFloat(formData.relevant_exp_months) >
        parseFloat(formData.exp_months))
    ) {
      showError("Relevant experience cannot be greater than total experience.");
      return;
    }

    const currentDate = new Date().setHours(0, 0, 0, 0);
    const selectedDate = new Date(formData.last_working_date).setHours(
      0,
      0,
      0,
      0
    );

    if (formData.serving_notice_period === "yes" && selectedDate < currentDate) {
      showError("Last working date cannot be in the past.");
      return;
    }

    if (!editData.resume_present && formData.resume === null) {
      showError("Resume is mandatory.");
      return;
    }
   
    // Prepare additional files data for API
 let pdfs = null;
let existing_file_ids = null;

const newFiles = selectedFiles;         // Newly added files
const oldFiles = existingFiles;         // Still-retained existing files
const removedIds = removedFileIds;      // Track removed file IDs

// ✅ Process newly added files
if (newFiles.length > 0) {
  pdfs = await Promise.all(
    newFiles.map(async (file) => ({
      filename: file.name,
      data: await fileToBase64(file),
      extension: `.${file.name.split(".").pop()}`
    }))
  );
}

// ✅ Process removed existing file IDs
if (removedIds.length > 0) {
  existing_file_ids = removedIds;
}


    const edit_candidate_data = {
      ...formData,
      id: location.state.item.id,
      user_id: localStorage.getItem("user_id"),
      expected_ctc: `${formData["expected_ctc_type"]} ${formData["expected_ctc_value"]}`,
      current_ctc: `${formData["current_ctc_type"]} ${formData["current_ctc_value"]}`,
      relevant_experience: `${formData["relevant_experience_years"]}.${formData["relevant_exp_months"]}`,
      experience: `${formData["experience_years"]}.${formData["exp_months"]}`,
      last_working_date: formData.last_working_date,
      // Additional files data
      remove_file_id: removedIds,
      pdfs: pdfs || [],
      // file_type: fileextension,
      // existing_file_ids: existing_file_ids || [],
      // removed_file_ids: removedFileIds || [],
    };
  console.log(edit_candidate_data, "edit_candidate_data");
    try {
      const response = await fetch(
        `http://192.168.0.47:5002/edit_candidate/${location.state.item.id}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(edit_candidate_data),
        }
      );
      const data = await response.json();

      if (data.status === "success") {

        getDashboardData().then(() => {
          setwaitForSubmission(false);
          toast.success(data.message);
          navigate("/dashboard");
        });
      } else {
        showError(data.message);
      }
    } catch (err) {
      console.error("Error:", err);
      showError("Something went wrong. Please try again.");
    }
  };

  useEffect(() => {
    localStorage.setItem("path", location.state.path);
    // Fetch existing files for the candidate
    // fetchCandidateData(editData.id);
  }, []);
  const shouldApplyNegativeMargin = existingFiles.length > 0 || selectedFiles.length > 0;
  return (
    <div className="wrapper">
      <LeftNav />
      <div className="section">
        <TitleBar />
        <div
          style={{}}
          className="candiedit"
        >
          <button
            className="back-button"
            onClick={() => navigate("/dashboard")}
            style={{ marginTop: "1px" }}
          >
            <FaArrowLeft />
          </button>
          <h5 className="headingtwo2">Edit Candidate Details</h5>
        </div>

        <div className="Container2 ">
          <form className="forms1" onSubmit={handleSubmit}>

            <div className="group">
              {USERTYPE === "managment" ? (
                <div className="JS">
                  <label htmlFor="job_selection_id">
                    <span className="required-field">*</span>Job ID:
                  </label>
                  <select
                    id="job_selection_id"
                    name="job_selection_id"
                    className="form-control"
                    value={formData.job_id}
                    onChange={handleJobChange}
                  >
                    <option value="" disabled>-- Select Job ID --</option>
                    {filteredJobs.map((job) => (
                      <option key={job.id} value={job.id}>
                        {job.id} - {job.client}
                      </option>
                    ))}
                  </select>
                </div>
              ) : null}
              <div className="JS">
                <label htmlFor="name">
                  <span className="required-field">*</span>Name:
                </label>
                <input
                  type="text"
                  id="client"
                  name="name"
                  className="form-control"
                  value={formData.name}
                  onChange={handleChange}
                />
                <small></small>
              </div>

              <div className="JS">
                <label htmlFor="mobile">
                  <span className="required-field">*</span>Mobile:
                </label>
                <input
                  type="text"
                  id="mobile"
                  name="mobile"
                  className="form-control"
                  value={formData.mobile}
                  onChange={handleChange}
                />
                <small></small>
              </div>

              <div className="JS">
                <label htmlFor="email">
                  <span className="required-field">*</span>Email:
                </label>
                <input
                  name="email"
                  type="email"
                  className="form-control"
                  value={formData.email}
                  onChange={handleChange}
                  required
                />
                <small></small>
              </div>

              <div className="JS">
                <label htmlFor="client">
                  <span className="required-field">*</span>Client:
                </label>
                <input
                  type="text"
                  id="client"
                  name="client"
                  className="form-control"
                  value={formData.client}
                  onChange={handleChange}
                  readOnly
                />
                <small></small>
              </div>

              <div className="JS">
                <label htmlFor="profile">
                  <span className="required-field">*</span>Profile:
                </label>
                <input
                  type="text"
                  id="profile"
                  name="profile"
                  className="form-control"
                  value={formData.profile}
                  onChange={handleChange}
                  readOnly
                />
                <small></small>
              </div>

              <div className="JS">
                <label htmlFor="skills">
                  <span className="required-field">*</span>Skills:
                </label>
                <input
                  type="text"
                  id="skills"
                  name="skills"
                  className="form-control"
                  value={formData.skills}
                  onChange={handleChange}
                />
                <small></small>
              </div>

              <div className="JS">
                <label htmlFor="current_company">Current Company:</label>
                <input
                  type="text"
                  id="current_company"
                  name="current_company"
                  className="form-control"
                  value={formData.current_company}
                  onChange={handleChange}
                />
              </div>

              <div className="JS">
                <label htmlFor="position">Position:</label>
                <input
                  type="text"
                  id="position"
                  name="position"
                  className="form-control"
                  value={formData.position}
                  onChange={handleChange}
                />
              </div>

              <div className="JS">
                <label htmlFor="reason_for_job_change">
                  Reason for Job Change:
                </label>
                <input
                  type="text"
                  id="reason_for_job_change"
                  name="reason_for_job_change"
                  className="form-control"
                  value={formData.reason_for_job_change}
                  onChange={handleChange}
                />
              </div>

              <div className="JS">
                <label htmlFor="resume">
                  <span className="required-field">*</span>Resume:{" "}
                  {editData.resume_present === true ? (
                    <span style={{ color: "green" }}>previously uploaded</span>
                  ) : (
                    <span style={{ color: "red" }}>
                      not uploaded previously
                    </span>
                  )}
                </label>
                <input
                  type="file"
                  id="resume"
                  name="resume"
                  className="resume1"
                  accept=".pdf,.doc,.docx"
                  autoComplete="off"
                  onChange={handleChange}
                />
                <small></small>
              </div>

            
              <div className="JS">
                <label htmlFor="current_job_location">
                  Current Job Location:
                </label>
                <input
                  type="text"
                  id="current_job_location"
                  name="current_job_location"
                  className="form-control"
                  value={formData.current_job_location}
                  onChange={handleChange}
                />
              </div>

              <div className="JS">
                <label htmlFor="preferred_job_location">
                  Preferred Job Location:
                </label>
                <input
                  type="text"
                  id="preferred_job_location"
                  name="preferred_job_location"
                  className="form-control"
                  value={formData.preferred_job_location}
                  onChange={handleChange}
                />
              </div>

              <div className="JS">
                <label htmlFor="experience">
                  <span style={{ color: "red" }}>*</span>Total Experience (Years):
                </label>
                <select
                  id="experience"
                  name="experience_years"
                  className="form-control"

                  value={formData.experience_years}
                  onChange={handleChange}
                >
                  <option value="" disabled>Select Years</option>
                  {exp_yrs.map((val) => (
                    <option key={val} value={val}>{val}</option>
                  ))}
                </select>
              </div>

              <div className="JS">
                <label htmlFor="exp_months">Months:</label>
                <select
                  id="exp_months"
                  name="exp_months"
                  className="form-control"
                  //style={selectStyle}
                  value={formData.exp_months}
                  onChange={handleChange}
                >
                  <option value="" disabled>Select Months</option>
                  {exp_month.map((val) => (
                    <option key={val} value={val}>{val}</option>
                  ))}
                </select>
              </div>

              <div className="JS">
                <label htmlFor="relevant_experience">
                  <span style={{ color: "red" }}>*</span>Relevant Experience (Years):
                </label>
                <select
                  id="relevant_experience"
                  name="relevant_experience_years"
                  className="form-control"
                  value={formData.relevant_experience_years}
                  onChange={handleChange}
                >
                  <option value="" disabled>Select Years</option>
                  {exp_yrs.map((val) => (
                    <option key={val} value={val}>{val}</option>
                  ))}
                </select>
              </div>

              <div className="JS">
                <label htmlFor="relevant_exp_months">Months:</label>
                <select
                  id="relevant_exp_months"
                  name="relevant_exp_months"
                  className="form-control"
                  value={formData.relevant_exp_months}
                  onChange={handleChange}
                >
                  <option value="" disabled>Select Months</option>
                  {exp_month.map((val) => (
                    <option key={val} value={val}>{val}</option>
                  ))}
                </select>
              </div>

              <div className="JS">
                <label htmlFor="currency_type_current">
                  <span style={{ color: "red" }}>*</span> Current CTC Currency Type:
                </label>
                <select

                  name="current_ctc_type"
                  className="form-control"
                  value={formData.current_ctc_type}
                  onChange={handleChange}
                >
                  <option value="INR">INR</option>
                  <option value="USD">USD</option>

                </select>
              </div>

              <div className="JS">
                <label htmlFor="current_ctc"><span style={{ color: "red" }}>*</span>Current CTC:</label>
                <input
                  type="number"
                  // id="current_ctc"
                  name="current_ctc_value"
                  className="form-control"
                  value={formData.current_ctc_value}
                  onChange={handleChange}
                />
              </div>

              <div className="JS">
                <label htmlFor="currency_type_except">
                  <span style={{ color: "red" }}>*</span>Expected CTC Currency Type:
                </label>
                <select
                  id="currency_type_except"
                  name="expected_ctc_type"
                  className="form-control"
                  value={formData.expected_ctc_type}
                  onChange={handleChange}
                >
                  <option value="INR">INR</option>
                  <option value="USD">USD</option>

                </select>
              </div>

              <div className="JS">
                <label htmlFor="expected_ctc"> <span style={{ color: "red" }}>*</span>Expected CTC:</label>
                <input
                  type="text"
                  id="expected_ctc"
                  name="expected_ctc_value"
                  className="form-control"
                  value={formData.expected_ctc_value}
                  onChange={handleChange}
                />
              </div>

              <div className="JS"
              //  style={{ marginTop: USERTYPE === "recruiter" ? "-60px" : "0px", }}
               >
                <label htmlFor="qualifications">Qualifications:</label>
                <input
                  type="text"
                  id="qualifications"
                  name="qualifications"
                  className="form-control"
                  value={formData.qualifications}
                  onChange={handleChange}
                />
              </div>

              {/* <div className="JS">
                <label htmlFor="notice_period">Notice Period:</label>
                <input
                  type="text"
                  id="notice_period"
                  name="notice_period"
                  className="form-control"
                  value={formData.notice_period}
                  onChange={handleChange}
                />
              </div> */}

              {/* <div className="JS">
                <label htmlFor="last_working_date">Last Working Date:</label>
                <input
                  type="text"
                  value=
                  id="last_working_date"
                  name="last_working_date"
                  className="form-control"
                  value={formData.last_working_date}
                  onChange={handleChange}
                />
              </div> */}


              <div className="JS">
                <label>
                  <span style={{ color: "red" }}>*</span>Serving Notice Period:
                </label>
                <select
                  required
                  className="input_style"
                  style={{ width: "100%" }}
                  name="serving_notice_period"
                  onChange={handleChange}
                  value={formData.serving_notice_period}
                >
                  <option value="" disabled>
                    Select
                  </option>
                  <option value="yes">Yes</option>
                  <option value="no">No</option>
                  <option value="completed">completed</option>
                </select>
                {formData.serving_notice_period === "yes" ? (
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      marginTop: "6px",
                    }}
                  >
                    <div style={{}}>
                      <label style={{ paddingRight: "3px", marginTop: "4px" }}>
                        <span style={{ color: "red" }}>*</span>Last Working
                        Date:{" "}
                      </label>
                      <input
                        type="date"
                        name="last_working_date"
                        id="last_working_date"
                        onChange={handleChange}
                        required
                        style={{ borderRadius: "4px", height: "28px" }}
                        value={formData.last_working_date}
                      />
                    </div>

                    <div style={{}}>
                      <label style={{ paddingRight: "3px", marginTop: "4px" }}>
                        Buyout:
                      </label>
                      <input
                        style={{ height: "25px", width: "25px" }}
                        name="buyout"
                        type="checkbox"
                        onChange={handleChange}
                        value={formData.buyout}
                        checked={formData.buyout}
                      />
                    </div>
                  </div>
                ) : formData.serving_notice_period === "no" ? (
                  <div
                    style={{
                      display: "flex",
                      marginTop: "6px",
                      justifyContent: "space-between",
                    }}
                  >
                    <div style={{}}>
                      <label style={{ paddingRight: "3px", marginTop: "4px" }}>
                        <span style={{ color: "red" }}>*</span>Notice period:{" "}
                      </label>
                      <select
                        required
                        onChange={handleChange}
                        name="period_of_notice"
                        value={formData.period_of_notice}
                      >
                        <option
                          value=""
                          selected={formData.period_of_notice === ""}
                        >
                          any
                        </option>
                        <option
                          value="0-15 days"
                          selected={formData.period_of_notice === "0-15 days"}
                        >
                          0-15 days
                        </option>
                        <option
                          value="1 Month"
                          selected={formData.period_of_notice === "1 Month"}
                        >
                          1 Month
                        </option>
                        <option
                          value="2 Months"
                          selected={formData.period_of_notice === "2 Months"}
                        >
                          2 Months
                        </option>
                        <option
                          value="3 Months"
                          selected={formData.period_of_notice === "3 Months"}
                        >
                          3 Months
                        </option>
                        <option
                          value="More than 3 Months"
                          selected={
                            formData.period_of_notice === "More than 3 Months"
                          }
                        >
                          More than 3 Months
                        </option>
                      </select>
                    </div>
                    <div style={{}}>
                      <label style={{ paddingRight: "3px", marginTop: "4px" }}>
                        Buyout:
                      </label>
                      <input
                        style={{ height: "25px", width: "25px" }}
                        name="buyout"
                        type="checkbox"
                        onChange={handleChange}
                        value={formData.buyout}
                        checked={formData.buyout}
                      />
                    </div>
                  </div>
                ) : (
                  <div> </div>
                )}
              </div>
              <div className="JS">
                <label>
                  <span style={{ color: "red" }}>*</span>Holding Offer:
                </label>
                <select
                  required
                  className="input_style"
                  style={{ width: "100%" }}
                  name="holding_offer"
                  onChange={handleChange}
                  value={formData.holding_offer}
                >
                  <option value="" disabled>
                    Select
                  </option>
                  <option value="yes">Yes</option>
                  <option value="no">No</option>
                  <option value="pipeline">pipeline</option>
                </select>

                {formData.holding_offer === "yes" ? (
                  <div
                    style={{
                      display: "flex",
                      marginTop: "6px",
                      justifyContent: "space-between",
                    }}
                  >
                    <div style={{}}>
                      <label style={{ paddingRight: "3px", marginTop: "4px" }}>
                        <span style={{ color: "red" }}>*</span>Total Offers:
                      </label>
                      <input
                        type="number" // Use "text" to have more control over the input
                        required
                        name="total_offers"
                        onChange={(e) => {
                          const value = e.target.value;

                          // Allow only digits and prevent symbols or negative values
                          if (/^\d*$/.test(value)) {
                            handleChange(e);
                          }
                        }}
                        style={{
                          borderRadius: "4px",
                          height: "28px",
                          width: "150px",
                        }}
                        value={formData.total_offers}
                      />
                    </div>

                    <div style={{}}>
                      <label style={{ paddingRight: "3px", marginTop: "4px" }}>
                        <span style={{ color: "red" }}>*</span>Highest Package in LPA:
                      </label>
                      <input
                        type="number" // Use "text" to have more control over the input
                        name="highest_package"
                        required
                        onChange={(e) => {
                          const value = e.target.value;

                          // Allow only positive numbers and prevent symbols or negative values
                          if (/^\d*\.?\d*$/.test(value)) {
                            handleChange(e);
                          }
                        }}
                        style={{ borderRadius: "4px", height: "28px" }}
                        value={formData.highest_package}
                      />
                    </div>


                  </div>
                ) : (
                  <div></div>
                )}
              </div>
              <div className="JS" style={{
                marginTop:
           shouldApplyNegativeMargin 
                    ? "-90px"
                    : "0px",
              }}>
                <label htmlFor="linkedin">Linkedin Profile:</label>
                <input
                  type="text"
                  id="linkedin"
                  name="linkedin"
                  className="form-control"
                  value={formData.linkedin}
                  onChange={handleChange}
                />
              </div>
                <div className="JS" style={{ width: "100%", maxHeight: "160px"}} >
                <label htmlFor="jd_pdf">
                  Additional files:
                </label>

                {/* File Upload Input */}
                <input
                  type="file"
                  id="jd_pdf"
                  accept=".pdf,.doc,.docx"
                  multiple
                  onChange={handleFileChange}
                />

                {/* Newly Selected Files Preview */}

                {(existingFiles.length > 0 || selectedFiles.length > 0) && (
                  <div className="additional-files-container">
                    <ul className="file-list">
                      {existingFiles.map((file, index) => (
                        <li key={`existing-${index}`} className="file-item">
                          <span className="file-name existing">
                            {file.filename}
                          </span>
                          <button
                            type="button"
                            onClick={() => handleRemoveFile(file.resume_id)}
                            className="remove-file-btn"
                            title="Remove file"
                          >
                            ❌
                          </button>
                        </li>
                      ))}
                      {selectedFiles.map((file, index) => (
                        <li key={`selected-${index}`} className="file-item">
                          <span className="file-name">{file.name}</span>
                          <button
                            type="button"
                            onClick={() => handleRemoveFilenew(index)}
                            className="remove-file-btn"
                            title="Remove file"
                          >
                            ❌
                          </button>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

              </div>
              <div className="JS">
                <label htmlFor="remarks">Remarks:</label>
                <textarea
                  id="remarks"
                  name="remarks"
                  className="form-control"
                  value={formData.remarks}
                  onChange={handleChange}
                  style={{ marginBottom: USERTYPE === "management" ? "0px" : "20px", }}
                ></textarea>
              </div>
            </div>
            <input type={'submit'} ref={last_working_date_ref} style={{ visibility: 'hidden' }} />
          </form>
        </div>
        <div className="buttons2">
          <div
            style={{ position: "relative", width: "160px", margin: "0 auto" }}
          >
            <input
              onClick={() => {
                last_working_date_ref.current.click()
              }}
              // onClick={handleSubmit}
              type="submit"
              value={waitForSubmission ? "" : "Update"}
              id="submits2"
            />
            <ThreeDots
              wrapperClass="ovalSpinner"
              wrapperStyle={{ position: "absolute", top: "-5px", left: "60px" }}
              visible={waitForSubmission}
              height="45"
              width="45"
              color="white"
              ariaLabel="oval-loading"
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default EditCandidate;