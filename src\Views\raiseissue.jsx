import React, { useState, useRef } from 'react';
import LeftNav from "../Components/LeftNav";
// import "../../Components/leftnav.css";
import TitleBar from "../Components/TitleBar";
import { toast } from "react-toastify";
import './raise.css';

const RaiseIssue = () => {
  const [images, setImages] = useState([]); // Multiple images
  const [issue, setIssue] = useState('');
  const pasteAreaRef = useRef(null);

  const handlePaste = (e) => {
    const items = e.clipboardData.items;
    const newImages = [];

    for (let item of items) {
      if (item.type.indexOf('image') !== -1) {
        const file = item.getAsFile();
        const reader = new FileReader();

        reader.onloadend = () => {
          setImages((prev) => [...prev, { file, base64: reader.result }]);
        };

        reader.readAsDataURL(file);
      }
    }
  };

  const handleCommentChange = (e) => {
    setIssue(e.target.value);
  };
  const handleRemoveImage = (indexToRemove) => {
    setImages((prev) => prev.filter((_, i) => i !== indexToRemove));
  };
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (images.length === 0 || !issue.trim()) {
      alert('Please paste at least one screenshot and enter a comment.');
      return;
    }
  const userId = localStorage.getItem("user_id");
    const payload = {
      screenshots: images.map(img => img.base64.split(',')[1]), // array of base64 strings
      issue,
      userId
    };

    try {
      const response = await fetch('http://192.168.0.47:5002/report_issue', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });
const data = await response.json();
      if (data.status === "success") {
        toast.success(data.message);
        setImages([]);
        setIssue('');
      }else {
  //       console.log("No additional files found for this candidate.");
    }
    } catch (error) {
      console.error('Error submitting issue:', error);
    }
  };

  return (
    <div className="wrapper">
      <LeftNav />
      <div className="section">
        <TitleBar />
        <div className="raise-container" onPaste={handlePaste}>
          <h2>Raise an Issue</h2>
          <form className="raise-form" onSubmit={handleSubmit}>
            <div
              className="paste-area"
              ref={pasteAreaRef}
              style={{overflow:"auto",height:"150px"}}
              tabIndex={0}
              onClick={() => pasteAreaRef.current.focus()}
            >
              {images.length > 0 ? (
                images.map((img, index) => (
                   <div key={index} style={{ position: "relative" }}>
                  <img
                    key={index}
                    src={img.base64}
                    alt={`Screenshot ${index + 1}`}
                    className="preview-img"
                  />
                    <button
                      type="button"
                      onClick={() => handleRemoveImage(index)}
                      style={{
                        position: "absolute",
                        top: "-6px",
                        right: "-6px",
                        background: "red",
                        color: "white",
                        border: "none",
                        borderRadius: "50%",
                        width: "20px",
                        height: "20px",
                        cursor: "pointer",
                        fontSize: "12px",
                      }}
                      title="Remove screenshot"
                    >
                      ×
                    </button>
                    </div>
                ))
              ) : (
                <p>Click here and paste your screenshot(s) (Ctrl + V)</p>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="comment">Issue Description</label>
              <textarea
                id="comment"
                style={{ height: "100px" }}
                placeholder="Describe the issue here..."
                value={issue}
                onChange={handleCommentChange}
              />
            </div>

            <button type="submit" className="raise-button">Submit Issue</button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default RaiseIssue;
