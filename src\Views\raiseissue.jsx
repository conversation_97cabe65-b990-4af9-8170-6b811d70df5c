import React, { useState, useRef,useEffect } from 'react';
import LeftNav from "../Components/LeftNav";
// import "../../Components/leftnav.css";
import TitleBar from "../Components/TitleBar";
import { ThreeDots } from "react-loader-spinner";
import { toast } from "react-toastify";
import Modal from "react-modal";
import './raise.css';

const RaiseIssue = () => {
  const [images, setImages] = useState([]); // Multiple images
  const [issue, setIssue] = useState('');
  const [waitForSubmission, setwaitForSubmission] = useState(false);
   const [modalMessage, setModalMessage] = useState('');
const [isPasteFocused, setIsPasteFocused] = useState(false);
  const pasteAreaRef = useRef(null);
    const [isModalOpen, setIsModalOpen] = useState(false);

    // Load images from localStorage on mount
  useEffect(() => {
    const savedImages = localStorage.getItem('raise_issue_images');
    if (savedImages) {
      setImages(JSON.parse(savedImages));
    }
  }, []);

  // Save images to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('raise_issue_images', JSON.stringify(images));
  }, [images]);


  const handlePaste = (e) => {
    const items = e.clipboardData.items;

    for (let item of items) {
      if (item.type.indexOf('image') !== -1) {
        const file = item.getAsFile();
        const reader = new FileReader();

        reader.onloadend = () => {
          setImages((prev) => [...prev, { file, base64: reader.result }]);
        };

        reader.readAsDataURL(file);
      }
    }
  };


  const handleCommentChange = (e) => {
    setIssue(e.target.value);
  };
  const handleRemoveImage = (indexToRemove) => {
    const updated = images.filter((_, i) => i !== indexToRemove);
    setImages(updated);
    localStorage.setItem('raise_issue_images', JSON.stringify(updated));
  };


 const handleSubmit = async (e) => {
    e.preventDefault();
    if (waitForSubmission) return;

    if (images.length === 0 ) {
      toast.warn('Please paste at least one screenshot.');   
      return;
    }
    if (!issue.trim()) {
  toast.warn('Please enter a comment about the issue.');
  return;
}

    setwaitForSubmission(true);
    const userId = localStorage.getItem("user_id");

    const payload = {
      screenshots: images.map(img => img.base64.split(',')[1]),
      issue,
      userId
    };

    try {
      const response = await fetch('http://192.168.0.47:5002/report_issue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });
      const data = await response.json();

      if (data.status === "success") {
        // toast.success(data.message);
        setModalMessage(data.message);
            setwaitForSubmission(true);
        setIsModalOpen(true);
        setImages([]);
        setIssue('');
        localStorage.removeItem('raise_issue_images');
        
      }
    toast.error(data.error)
     setwaitForSubmission(false);
    } catch (error) {
      console.error('Error submitting issue:', error);
          setwaitForSubmission(false);
    } finally {
      setwaitForSubmission(false);
    }
  };

  return (
    <div className="wrapper">
      <LeftNav />
      <div className="section">
        <TitleBar />
        <div className="raise-container" onPaste={handlePaste}>
          <h2>Help & Support</h2>
          <form className="raise-form" onSubmit={handleSubmit}>
            <div
              className="paste-area"
              ref={pasteAreaRef}
              style={{overflow:"auto",height:"150px", cursor: isPasteFocused?"text":"pointer",}}
              tabIndex={0}
         onFocus={() => setIsPasteFocused(true)}
  onBlur={() => setIsPasteFocused(false)}
              onClick={() => pasteAreaRef.current.focus()}
            >
              {images.length > 0 ? (
                images.map((img, index) => (
                   <div key={index} style={{ position: "relative" }}>
                  <img
                    key={index}
                    src={img.base64}
                    alt={`Screenshot ${index + 1}`}
                    className="preview-img"
                  />
                    <button
                      type="button"
                      onClick={() => handleRemoveImage(index)}
                      style={{
                        position: "absolute",
                        top: "-6px",
                        right: "-6px",
                        background: "red",
                        color: "white",
                        border: "none",
                        borderRadius: "50%",
                        width: "20px",
                        height: "20px",
                        cursor: "pointer",
                        fontSize: "12px",
                      }}
                      title="Remove screenshot"
                    >
                      ×
                    </button>
                    </div>
                ))
              ) : (
           
      <p
          style={{
        fontSize: "14px",
        color: isPasteFocused ? "#888" : "red",
        // position: "absolute",
        // top: "10px",
        // left: "10px",
        // margin: 0,
      }}
      >Click here and paste your screenshot(s) (Ctrl + V)</p>

              )}
            </div>

            <div className="form-group">
              <label htmlFor="comment">Issue Description</label>
              <textarea
                id="comment"
                style={{ height: "100px" }}
                placeholder="Describe the issue here..."
                value={issue}
                onChange={handleCommentChange}
              />
            </div>

            <button type="submit"   style={{ position: "relative", }} className="raise-button"        >
                         {waitForSubmission ? "" : "Raise Issue"}
                        <ThreeDots
                            wrapperClass="ovalSpinner"
                            wrapperStyle={{
                              position: "absolute",
                              top: "-3px",
                              right: "255px",
                              // background:"green"
                            }}
                            visible={waitForSubmission}
                            height="45"
                            width="45"
                            color="white"
                            ariaLabel="oval-loading"
                          /> 
                          </button>
          </form>
        </div>
<Modal
  isOpen={isModalOpen}
  onRequestClose={() => setIsModalOpen(false)} // Optional for escape key and click outside
  style={{
    overlay: {
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      zIndex: 9999,
    },
    content: {
      top: '50%',
      left: '50%',
      right: 'auto',
      bottom: 'auto',
      transform: 'translate(-50%, -50%)',
      width: '400px',
      borderRadius: '12px',
      padding: '30px 20px',
      backgroundColor: '#fff',
      boxShadow: '0 8px 20px rgba(0, 0, 0, 0.2)',
      transition: 'all 0.3s ease-in-out',
    }
  }}
>
  <div style={{ textAlign: 'center' }}>
    <div style={{ fontSize: '24px', fontWeight: '600', color: '#2d3748' }}>
       Success!
    </div>
    <p style={{ color: '#4a5568', marginTop: '12px', fontSize: '16px' }}>
      {modalMessage}
    </p>
  </div>

  <div style={{ display: 'flex', justifyContent: 'center', marginTop: '25px' }}>
    <button
      onClick={() => setIsModalOpen(false)}
      style={{
        padding: '10px 24px',
        background: 'linear-gradient(135deg, #38a169, #48bb78)',
        color: '#fff',
        border: 'none',
        borderRadius: '6px',
        fontSize: '14px',
        fontWeight: '500',
        cursor: 'pointer',
        boxShadow: '0 2px 8px rgba(56, 161, 105, 0.3)',
        transition: 'background 0.3s ease',
      }}
      onMouseOver={(e) =>
        (e.target.style.background = 'linear-gradient(135deg, #2f855a, #38a169)')
      }
      onMouseOut={(e) =>
        (e.target.style.background = 'linear-gradient(135deg, #38a169, #48bb78)')
      }
    >
      OK
    </button>
  </div>
</Modal>

      </div>
    </div>
  );
};

export default RaiseIssue;
